codeunit 60032 "Item Price Dataset By Loc IPK"
{
    var
        ProcessingCompletedMsg: Label 'Processing completed for %1 items. %2 location-specific records created, %3 records populated with data. Processing time: %4 minutes.', Comment = '%1 = Number of items, %2 = Number of created records, %3 = Number of populated records, %4 = Processing time in minutes';
        LocationProcessingCompletedMsg: Label 'Processing completed for location %1. %2 items processed, %3 records created, %4 records populated with data. Processing time: %5 minutes.', Comment = '%1 = Location code, %2 = Number of items, %3 = Number of created records, %4 = Number of populated records, %5 = Processing time in minutes';
        ErrLocationDoesNotExistLbl: Label 'Location %1 does not exist.', Comment = '%1 = Location code';

    procedure ProcessAllItems()
    var
        Item: Record Item;
        Location: Record Location;
        ItemLedgerEntry: Record "Item Ledger Entry";
        ProcessingTimeMs: BigInteger;
        StartTime: DateTime;
        EndTime: DateTime;
        ProcessingTimeMinutes: Decimal;
        CreatedRecords: Integer;
        PopulatedRecords: Integer;
        TotalCreated: Integer;
        TotalPopulated: Integer;
        ItemCount: Integer;
        LocationItemCombinations: Dictionary of [Text, Boolean];
        CombinationKey: Text;
    begin
        // Record start time
        StartTime := CurrentDateTime();

        // Initialize counters
        TotalCreated := 0;
        TotalPopulated := 0;
        ItemCount := 0;

        // First, collect all unique Item+Location combinations from Item Ledger Entries
        ItemLedgerEntry.Reset();
        ItemLedgerEntry.SetCurrentKey("Item No.", "Location Code");
        if ItemLedgerEntry.FindSet() then
            repeat
                // Only process non-blocked items and valid locations
                if Item.Get(ItemLedgerEntry."Item No.") and (not Item.Blocked) then
                    if Location.Get(ItemLedgerEntry."Location Code") then
                        CombinationKey := ItemLedgerEntry."Item No." + '|' + ItemLedgerEntry."Location Code";
                if not LocationItemCombinations.ContainsKey(CombinationKey) then
                    LocationItemCombinations.Add(CombinationKey, true);
            until ItemLedgerEntry.Next() = 0;

        // Process each unique Item+Location combination
        foreach CombinationKey in LocationItemCombinations.Keys() do begin
            if ParseItemLocationCombination(CombinationKey, Item."No.", Location.Code) then
                ItemCount += 1;
            // Create monthly structure for this specific item+location combination
            CreatedRecords := CreateMonthlyStructureForItemLocation(Item."No.", Location.Code);
            TotalCreated += CreatedRecords;
        end;

        // Count populated records
        PopulatedRecords := CountPopulatedRecords();
        TotalPopulated := PopulatedRecords;

        // Record end time and calculate duration
        EndTime := CurrentDateTime();
        ProcessingTimeMs := EndTime - StartTime;
        ProcessingTimeMinutes := ProcessingTimeMs / 60000; // Convert milliseconds to minutes

        // Show completion message with timing
        Message(ProcessingCompletedMsg,
            ItemCount, TotalCreated, TotalPopulated, Round(ProcessingTimeMinutes, 0.01));
    end;

    procedure ProcessItemsForLocation(LocationCode: Code[10])
    var
        Item: Record Item;
        Location: Record Location;
        ItemLedgerEntry: Record "Item Ledger Entry";
        ProcessingTimeMs: BigInteger;
        StartTime: DateTime;
        EndTime: DateTime;
        ProcessingTimeMinutes: Decimal;
        CreatedRecords: Integer;
        PopulatedRecords: Integer;
        TotalCreated: Integer;
        TotalPopulated: Integer;
        ItemCount: Integer;
        Items: List of [Code[20]];
        ItemNo: Code[20];
    begin
        // Validate location exists
        if not Location.Get(LocationCode) then
            Error(ErrLocationDoesNotExistLbl, LocationCode);

        // Record start time
        StartTime := CurrentDateTime();

        // Initialize counters
        TotalCreated := 0;
        TotalPopulated := 0;
        ItemCount := 0;

        // Find all unique items at this location
        ItemLedgerEntry.Reset();
        ItemLedgerEntry.SetRange("Location Code", LocationCode);
        ItemLedgerEntry.SetCurrentKey("Item No.", "Location Code");
        if ItemLedgerEntry.FindSet() then
            repeat
                if Item.Get(ItemLedgerEntry."Item No.") and (not Item.Blocked) then
                    if not Items.Contains(ItemLedgerEntry."Item No.") then
                        Items.Add(ItemLedgerEntry."Item No.");
            until ItemLedgerEntry.Next() = 0;

        // Process each item at this location
        foreach ItemNo in Items do begin
            ItemCount += 1;

            // Create monthly structure for this specific item+location combination
            CreatedRecords := CreateMonthlyStructureForItemLocation(ItemNo, LocationCode);
            TotalCreated += CreatedRecords;
        end;

        // Count populated records for this location
        PopulatedRecords := CountPopulatedRecordsForLocation(LocationCode);
        TotalPopulated := PopulatedRecords;

        // Record end time and calculate duration
        EndTime := CurrentDateTime();
        ProcessingTimeMs := EndTime - StartTime;
        ProcessingTimeMinutes := ProcessingTimeMs / 60000; // Convert milliseconds to minutes

        // Show completion message with timing
        Message(LocationProcessingCompletedMsg,
            LocationCode, ItemCount, TotalCreated, TotalPopulated, Round(ProcessingTimeMinutes, 0.01));
    end;

    local procedure CreateMonthlyStructureForItemLocation(ItemNo: Code[20]; LocationCode: Code[10]): Integer
    var
        Item: Record Item;
        Location: Record Location;
        DatasetRecord: Record "Item Price Dataset By Loc IPK";
        LastRecord: Record "Item Price Dataset By Loc IPK";
        ItemLedgerEntry: Record "Item Ledger Entry";
        ItemVariants: List of [Code[10]];
        VariantCode: Code[10];
        StartDate: Date;
        EndDate: Date;
        CreatedCount: Integer;
        MonthNo: Integer;
        NextEntryNo: Integer;
        MonthText: Text;
        CurrentMonth: Integer;
    begin
        // Get item and location information
        if not Item.Get(ItemNo) then
            exit(0);
        if not Location.Get(LocationCode) then
            exit(0);

        // Get current month
        CurrentMonth := WorkDate().Month();

        // Get next Entry No.
        if LastRecord.FindLast() then
            NextEntryNo := LastRecord."Entry No." + 1
        else
            NextEntryNo := 1;

        // Find all unique Variant Codes for this item+location combination in Item Ledger Entries
        ItemLedgerEntry.SetRange("Item No.", ItemNo);
        ItemLedgerEntry.SetRange("Location Code", LocationCode);
        if ItemLedgerEntry.FindSet() then
            repeat
                if not ItemVariants.Contains(ItemLedgerEntry."Variant Code") then
                    ItemVariants.Add(ItemLedgerEntry."Variant Code");
            until ItemLedgerEntry.Next() = 0;

        // If no variants found, add empty variant
        if ItemVariants.Count() = 0 then
            ItemVariants.Add('');

        // Create December 2024 record first for each variant
        foreach VariantCode in ItemVariants do begin
            MonthText := '12.2024';
            StartDate := DMY2Date(1, 12, 2024); // First day of December 2024
            EndDate := CalcDate('<CM>', StartDate); // Last day of December 2024

            // Create dataset record for December 2024
            DatasetRecord.Init();
            DatasetRecord."Entry No." := NextEntryNo;
            DatasetRecord."Item No." := ItemNo;
            DatasetRecord."Variant Code" := VariantCode;
            DatasetRecord."Location Code" := LocationCode;
            DatasetRecord.Validate("Location Code");
            DatasetRecord."Description" := Item.Description;
            DatasetRecord."Base Unit of Measure Code" := Item."Base Unit of Measure";
            DatasetRecord."Source Price Month" := CopyStr(MonthText, 1, 7);
            DatasetRecord."Start Date" := StartDate;
            DatasetRecord."End Date" := EndDate;
            DatasetRecord."Average Unit Cost (ACY)" := 0;
            DatasetRecord."Average Unit Cost (LCY)" := 0;

            DatasetRecord.Insert(true);

            // Calculate quantities and pricing for the newly created dataset record
            CalculateQuantityForDatasetRecord(DatasetRecord);
            CalculateCostForDatasetRecord(DatasetRecord);
            CalculateSalesForDatasetRecord(DatasetRecord);

            CreatedCount += 1;
            NextEntryNo += 1;

            // Create records for each month from January to current month in 2025
            for MonthNo := 1 to CurrentMonth do begin
                // Format month as MM.YYYY
                if MonthNo < 10 then
                    MonthText := '0' + Format(MonthNo) + '.2025'
                else
                    MonthText := Format(MonthNo) + '.2025';

                // Calculate start and end dates for the month
                StartDate := DMY2Date(1, MonthNo, 2025); // First day of month
                EndDate := CalcDate('<CM>', StartDate); // Last day of month

                // Create dataset record
                DatasetRecord.Init();
                DatasetRecord."Entry No." := NextEntryNo;
                DatasetRecord."Item No." := ItemNo;
                DatasetRecord."Variant Code" := VariantCode;
                DatasetRecord."Location Code" := LocationCode;
                DatasetRecord.Validate("Location Code");
                DatasetRecord."Description" := Item.Description;
                DatasetRecord."Base Unit of Measure Code" := Item."Base Unit of Measure";
                DatasetRecord."Source Price Month" := CopyStr(MonthText, 1, 7);
                DatasetRecord."Start Date" := StartDate;
                DatasetRecord."End Date" := EndDate;
                DatasetRecord."Average Unit Cost (ACY)" := 0;
                DatasetRecord."Average Unit Cost (LCY)" := 0;

                DatasetRecord.Insert(true);

                // Calculate quantities and pricing for the newly created dataset record
                CalculateQuantityForDatasetRecord(DatasetRecord);
                CalculateCostForDatasetRecord(DatasetRecord);
                CalculateSalesForDatasetRecord(DatasetRecord);

                CreatedCount += 1;
                NextEntryNo += 1;
            end;
        end;

        exit(CreatedCount);
    end;

    local procedure CalculateQuantityForDatasetRecord(var DatasetRecord: Record "Item Price Dataset By Loc IPK")
    var
        ILE: Record "Item Ledger Entry";
    begin
        // Calculate and set Quantity End of Month using CalcSums
        ILE.Reset();
        ILE.SetRange("Item No.", DatasetRecord."Item No.");
        if DatasetRecord."Variant Code" <> '' then
            ILE.SetRange("Variant Code", DatasetRecord."Variant Code")
        else
            ILE.SetRange("Variant Code", '');
        ILE.SetRange("Location Code", DatasetRecord."Location Code");
        ILE.SetFilter("Posting Date", '..%1', DatasetRecord."End Date");
        ILE.CalcSums(Quantity);
        DatasetRecord."Quantity End of Month IPK" := ILE.Quantity;
        DatasetRecord.Modify(true);
    end;

    local procedure CalculateCostForDatasetRecord(var DatasetRecord: Record "Item Price Dataset By Loc IPK"): Boolean
    var
        ValueEntry: Record "Value Entry";
        ACYCurrencyCode: Code[10];
        CostAmountACY: Decimal;
        CostAmountLCY: Decimal;
        TotalCostAmountACY: Decimal;
        TotalCostAmountLCY: Decimal;
        TotalQuantity: Decimal;
        WeightedAvgCostACY: Decimal;
        WeightedAvgCostLCY: Decimal;
    begin
        // Get ACY currency code
        ACYCurrencyCode := GetACYCurrencyCode();

        // Initialize totals
        TotalCostAmountACY := 0;
        TotalCostAmountLCY := 0;
        TotalQuantity := 0;

        // Filter Value Entries for this specific dataset record
        ValueEntry.Reset();
        ValueEntry.SetRange("Item No.", DatasetRecord."Item No.");

        // Handle Variant Code - if dataset has blank variant, filter for blank; otherwise use specific variant
        if DatasetRecord."Variant Code" = '' then
            ValueEntry.SetRange("Variant Code", '')
        else
            ValueEntry.SetRange("Variant Code", DatasetRecord."Variant Code");

        ValueEntry.SetRange("Location Code", DatasetRecord."Location Code");
        ValueEntry.SetRange("Posting Date", DatasetRecord."Start Date", DatasetRecord."End Date");

        // Filter for Purchase Invoice, Purchase Credit Memo, and blank document types
        ValueEntry.SetFilter("Document Type", '%1|%2|%3',
            ValueEntry."Document Type"::"Purchase Invoice",
            ValueEntry."Document Type"::"Purchase Credit Memo",
            ValueEntry."Document Type"::" ");

        // Filter for Purchase and Output entry types
        ValueEntry.SetFilter("Item Ledger Entry Type", '%1|%2',
            ValueEntry."Item Ledger Entry Type"::Purchase,
            ValueEntry."Item Ledger Entry Type"::Output);
        ValueEntry.SetFilter("Entry Type", '<>%1', ValueEntry."Entry Type"::Rounding);
        ValueEntry.SetRange(Adjustment, false);

        // Collect cost data from Value Entries
        if ValueEntry.FindSet() then
            repeat
                // Get ACY amount - use ACY amount if available, otherwise fall back to LCY
                CostAmountACY := ValueEntry."Cost Amount (Actual) (ACY)";
                if CostAmountACY = 0 then
                    CostAmountACY := ValueEntry."Cost Amount (Actual)";

                CostAmountLCY := ValueEntry."Cost Amount (Actual)";

                // Accumulate amounts and quantities
                TotalCostAmountACY += CostAmountACY;
                TotalCostAmountLCY += CostAmountLCY;
                TotalQuantity += ValueEntry."Valued Quantity";
            until ValueEntry.Next() = 0;

        // Calculate weighted average costs if we have quantity
        if TotalQuantity <> 0 then begin
            WeightedAvgCostACY := TotalCostAmountACY / TotalQuantity;
            WeightedAvgCostLCY := TotalCostAmountLCY / TotalQuantity;

            // Update the dataset record
            DatasetRecord."Average Unit Cost (ACY)" := Abs(WeightedAvgCostACY);
            DatasetRecord."Average Unit Cost (LCY)" := Abs(WeightedAvgCostLCY);
            DatasetRecord."Currency Code" := ACYCurrencyCode;
            DatasetRecord.Modify(true);

            exit(true); // Successfully calculated and updated
        end;

        // If no current month data found, try to get data from previous months
        if GetPreviousMonthCostData(DatasetRecord) then
            exit(true); // Successfully used previous month data

        exit(false); // No data found from current or previous months
    end;

    local procedure CalculateSalesForDatasetRecord(var DatasetRecord: Record "Item Price Dataset By Loc IPK"): Boolean
    var
        ValueEntry: Record "Value Entry";
        ACYCurrencyCode: Code[10];
        SalesAmountLCY: Decimal;
        SalesAmountACY: Decimal;
        TotalSalesAmountLCY: Decimal;
        TotalSalesAmountACY: Decimal;
        TotalSalesQuantity: Decimal;
        WeightedAvgSalesPriceLCY: Decimal;
        WeightedAvgSalesPriceACY: Decimal;
    begin
        // Get ACY currency code
        ACYCurrencyCode := GetACYCurrencyCode();

        // Initialize totals
        TotalSalesAmountLCY := 0;
        TotalSalesAmountACY := 0;
        TotalSalesQuantity := 0;

        // Filter Value Entries for this specific dataset record
        ValueEntry.Reset();
        ValueEntry.SetRange("Item No.", DatasetRecord."Item No.");

        // Handle Variant Code - if dataset has blank variant, filter for blank; otherwise use specific variant
        if DatasetRecord."Variant Code" = '' then
            ValueEntry.SetRange("Variant Code", '')
        else
            ValueEntry.SetRange("Variant Code", DatasetRecord."Variant Code");

        ValueEntry.SetRange("Location Code", DatasetRecord."Location Code");
        ValueEntry.SetRange("Posting Date", DatasetRecord."Start Date", DatasetRecord."End Date");

        // Filter for Sales Invoice, Sales Credit Memo, and blank document types
        ValueEntry.SetFilter("Document Type", '%1|%2|%3',
            ValueEntry."Document Type"::"Sales Invoice",
            ValueEntry."Document Type"::"Sales Credit Memo",
            ValueEntry."Document Type"::" ");

        // Filter for Sale entry type
        ValueEntry.SetFilter("Item Ledger Entry Type", '%1',
            ValueEntry."Item Ledger Entry Type"::Sale);
        ValueEntry.SetFilter("Entry Type", '<>%1', ValueEntry."Entry Type"::Rounding);
        ValueEntry.SetRange(Adjustment, false);

        // Collect sales data from Value Entries
        if ValueEntry.FindSet() then
            repeat
                SalesAmountLCY := ValueEntry."Sales Amount (Actual)";
                SalesAmountACY := ValueEntry."Sales Amount (Actual)"; // This can be enhanced with currency conversion

                TotalSalesAmountLCY += SalesAmountLCY;
                TotalSalesAmountACY += SalesAmountACY;
                TotalSalesQuantity += ValueEntry."Valued Quantity";
            until ValueEntry.Next() = 0;

        // Calculate weighted average sales price if we have quantity
        if TotalSalesQuantity <> 0 then begin
            WeightedAvgSalesPriceLCY := TotalSalesAmountLCY / TotalSalesQuantity;
            WeightedAvgSalesPriceACY := TotalSalesAmountACY / TotalSalesQuantity;

            // Update the dataset record
            DatasetRecord."Average Unit Price (LCY)" := Abs(WeightedAvgSalesPriceLCY);
            DatasetRecord."Average Unit Price (ACY)" := Abs(WeightedAvgSalesPriceACY);
            DatasetRecord."Currency Code" := ACYCurrencyCode;
            DatasetRecord.Modify(true);

            exit(true); // Successfully calculated and updated
        end;

        // If no current month data found, try to get data from previous months
        if GetPreviousMonthSalesData(DatasetRecord) then
            exit(true); // Successfully used previous month sales data

        exit(false); // No data found from current or previous months
    end;

    local procedure GetPreviousMonthCostData(var DatasetRecord: Record "Item Price Dataset By Loc IPK"): Boolean
    var
        PreviousDatasetRecord: Record "Item Price Dataset By Loc IPK";
    begin
        // Look for dataset record with same item, variant, and location for dates before current record
        PreviousDatasetRecord.Reset();
        PreviousDatasetRecord.SetRange("Item No.", DatasetRecord."Item No.");
        PreviousDatasetRecord.SetRange("Variant Code", DatasetRecord."Variant Code");
        PreviousDatasetRecord.SetRange("Location Code", DatasetRecord."Location Code");
        PreviousDatasetRecord.SetFilter("Start Date", '<%1', DatasetRecord."Start Date");
        PreviousDatasetRecord.SetCurrentKey("Start Date");

        // Find the latest record that has cost data
        if PreviousDatasetRecord.FindLast() then begin
            DatasetRecord."Average Unit Cost (ACY)" := PreviousDatasetRecord."Average Unit Cost (ACY)";
            DatasetRecord."Average Unit Cost (LCY)" := PreviousDatasetRecord."Average Unit Cost (LCY)";
            DatasetRecord."Currency Code" := PreviousDatasetRecord."Currency Code";
            DatasetRecord.Modify(true);
            exit(true); // Successfully found and applied previous month data
        end;

        exit(false); // No previous month data found
    end;

    local procedure GetPreviousMonthSalesData(var DatasetRecord: Record "Item Price Dataset By Loc IPK"): Boolean
    var
        PreviousDatasetRecord: Record "Item Price Dataset By Loc IPK";
    begin
        // Look for dataset record with same item, variant, and location for dates before current record
        PreviousDatasetRecord.Reset();
        PreviousDatasetRecord.SetRange("Item No.", DatasetRecord."Item No.");
        PreviousDatasetRecord.SetRange("Variant Code", DatasetRecord."Variant Code");
        PreviousDatasetRecord.SetRange("Location Code", DatasetRecord."Location Code");
        PreviousDatasetRecord.SetFilter("Start Date", '<%1', DatasetRecord."Start Date");
        PreviousDatasetRecord.SetCurrentKey("Start Date");

        // Find the latest record that has sales price data
        if PreviousDatasetRecord.FindLast() then begin
            DatasetRecord."Average Unit Price (LCY)" := PreviousDatasetRecord."Average Unit Price (LCY)";
            DatasetRecord."Average Unit Price (ACY)" := PreviousDatasetRecord."Average Unit Price (ACY)";
            DatasetRecord."Currency Code" := PreviousDatasetRecord."Currency Code";
            DatasetRecord.Modify(true);
            exit(true); // Successfully found and applied previous month sales data
        end;

        exit(false); // No previous month sales data found
    end;

    local procedure ParseItemLocationCombination(CombinationKey: Text; var ItemNo: Code[20]; var LocationCode: Code[10]): Boolean
    var
        SeparatorPos: Integer;
        ItemNoText: Text;
        LocationCodeText: Text;
    begin
        SeparatorPos := StrPos(CombinationKey, '|');
        if SeparatorPos = 0 then
            exit(false);

        ItemNoText := CopyStr(CombinationKey, 1, SeparatorPos - 1);
        LocationCodeText := CopyStr(CombinationKey, SeparatorPos + 1);

        // Ensure the text fits in the Code fields
        if StrLen(ItemNoText) > MaxStrLen(ItemNo) then
            exit(false);
        if StrLen(LocationCodeText) > MaxStrLen(LocationCode) then
            exit(false);

        ItemNo := CopyStr(ItemNoText, 1, MaxStrLen(ItemNo));
        LocationCode := CopyStr(LocationCodeText, 1, MaxStrLen(LocationCode));

        exit(true);
    end;

    local procedure CountPopulatedRecords(): Integer
    var
        DatasetRecord: Record "Item Price Dataset By Loc IPK";
    begin
        DatasetRecord.SetFilter("Average Unit Cost (ACY)", '<>%1', 0);
        exit(DatasetRecord.Count());
    end;

    local procedure CountPopulatedRecordsForLocation(LocationCode: Code[10]): Integer
    var
        DatasetRecord: Record "Item Price Dataset By Loc IPK";
    begin
        DatasetRecord.SetRange("Location Code", LocationCode);
        DatasetRecord.SetFilter("Average Unit Cost (ACY)", '<>%1', 0);
        exit(DatasetRecord.Count());
    end;

    local procedure GetACYCurrencyCode(): Code[10]
    var
        GeneralLedgerSetup: Record "General Ledger Setup";
    begin
        if GeneralLedgerSetup.Get() then
            exit(GeneralLedgerSetup."Additional Reporting Currency");

        exit('');
    end;

    procedure DrillDownCostValueEntries(DatasetRecord: Record "Item Price Dataset By Loc IPK")
    var
        ValueEntry: Record "Value Entry";
        ValueEntriesPage: Page "Value Entries";
    begin
        // Filter Value Entries for cost calculation
        ValueEntry.Reset();
        ValueEntry.SetRange("Item No.", DatasetRecord."Item No.");

        // Handle Variant Code - if dataset has blank variant, filter for blank; otherwise use specific variant
        if DatasetRecord."Variant Code" = '' then
            ValueEntry.SetRange("Variant Code", '')
        else
            ValueEntry.SetRange("Variant Code", DatasetRecord."Variant Code");

        ValueEntry.SetRange("Location Code", DatasetRecord."Location Code");
        ValueEntry.SetRange("Posting Date", DatasetRecord."Start Date", DatasetRecord."End Date");

        // Filter for Purchase Invoice, Purchase Credit Memo, and blank document types
        ValueEntry.SetFilter("Document Type", '%1|%2|%3',
            ValueEntry."Document Type"::"Purchase Invoice",
            ValueEntry."Document Type"::"Purchase Credit Memo",
            ValueEntry."Document Type"::" ");

        // Filter for Purchase and Output entry types
        ValueEntry.SetFilter("Item Ledger Entry Type", '%1|%2',
            ValueEntry."Item Ledger Entry Type"::Purchase,
            ValueEntry."Item Ledger Entry Type"::Output);
        ValueEntry.SetFilter("Entry Type", '<>%1', ValueEntry."Entry Type"::Rounding);
        ValueEntry.SetRange(Adjustment, false);

        // Open filtered Value Entries page
        ValueEntriesPage.SetTableView(ValueEntry);
        ValueEntriesPage.Run();
    end;

    procedure DrillDownSalesValueEntries(DatasetRecord: Record "Item Price Dataset By Loc IPK")
    var
        ValueEntry: Record "Value Entry";
        ValueEntriesPage: Page "Value Entries";
    begin
        // Filter Value Entries for sales calculation
        ValueEntry.Reset();
        ValueEntry.SetRange("Item No.", DatasetRecord."Item No.");

        // Handle Variant Code - if dataset has blank variant, filter for blank; otherwise use specific variant
        if DatasetRecord."Variant Code" = '' then
            ValueEntry.SetRange("Variant Code", '')
        else
            ValueEntry.SetRange("Variant Code", DatasetRecord."Variant Code");

        ValueEntry.SetRange("Location Code", DatasetRecord."Location Code");
        ValueEntry.SetRange("Posting Date", DatasetRecord."Start Date", DatasetRecord."End Date");

        // Filter for Sales Invoice, Sales Credit Memo, and blank document types
        ValueEntry.SetFilter("Document Type", '%1|%2|%3',
            ValueEntry."Document Type"::"Sales Invoice",
            ValueEntry."Document Type"::"Sales Credit Memo",
            ValueEntry."Document Type"::" ");

        // Filter for Sale entry type
        ValueEntry.SetFilter("Item Ledger Entry Type", '%1',
            ValueEntry."Item Ledger Entry Type"::Sale);
        ValueEntry.SetFilter("Entry Type", '<>%1', ValueEntry."Entry Type"::Rounding);
        ValueEntry.SetRange(Adjustment, false);

        // Open filtered Value Entries page
        ValueEntriesPage.SetTableView(ValueEntry);
        ValueEntriesPage.Run();
    end;
}
