codeunit 60070 "Source Lot No. Migration IPK"
{
    /// <summary>
    /// Migrates historical Item Ledger Entries to populate the "Source Lot No. IPK" field
    /// - Output entries: Source Lot No. = Lot No.
    /// - Consumption entries: Source Lot No. = Related NEXT output entry's Lot No. (consumption feeds into output)
    /// </summary>
    Permissions = tabledata "Item Ledger Entry" = rimd;

    var
        ConfirmManagement: Codeunit "Confirm Management";
        OutputEntryMsg: Label 'Output Entry No. %1', Comment = '%1=EntryNo';
        ConsumptionEntryMsg: Label 'Consumption Entry No. %1', Comment = '%1=EntryNo';
        ConfirmMigrationMsg: Label 'Found %1 entries to migrate. Do you want to continue?', Comment = '%1=TotalCount';
        ConfirmDateRangeMsg: Label 'Found %1 entries to migrate in date range %2..%3. Continue?', Comment = '%1=TotalCount; %2=FromDate; %3=ToDate';

    procedure MigrateHistoricalSourceLotNo()
    var
        ItemLedgerEntry: Record "Item Ledger Entry";
        Window: Dialog;
        UpdatedCount: Integer;
        TotalCount: Integer;
        MigrationStartMsg: Label 'Starting migration of historical Source Lot No. data...';
        MigrationCompleteMsg: Label 'Migration completed successfully!\Output entries updated: %1\Consumption entries updated: %2\Total entries processed: %3', Comment = '%1=OutputUpdatedCount; %2=ConsumptionUpdatedCount; %3=UpdatedCount';
        OutputUpdatedCount: Integer;
        ConsumptionUpdatedCount: Integer;
    begin
        Window.Open(MigrationStartMsg + '\\Processing: #1########## \Updated: #2########');

        // Count total entries to process
        ItemLedgerEntry.SetFilter("Source Lot No. IPK", '=%1', '');
        TotalCount := ItemLedgerEntry.Count();

        if TotalCount = 0 then begin
            Message('No entries found that need migration.');
            exit;
        end;

        if not ConfirmManagement.GetResponseOrDefault(StrSubstNo(ConfirmMigrationMsg, TotalCount), false) then
            exit;

        // Process Output entries first
        ItemLedgerEntry.Reset();
        ItemLedgerEntry.SetRange("Entry Type", ItemLedgerEntry."Entry Type"::Output);
        ItemLedgerEntry.SetFilter("Source Lot No. IPK", '=%1', '');
        ItemLedgerEntry.SetFilter("Lot No.", '<>%1', '');

        if ItemLedgerEntry.FindSet() then
            repeat
                UpdatedCount += 1;
                Window.Update(1, StrSubstNo(OutputEntryMsg, ItemLedgerEntry."Entry No."));
                Window.Update(2, UpdatedCount);

                ItemLedgerEntry."Source Lot No. IPK" := ItemLedgerEntry."Lot No.";
                ItemLedgerEntry.Modify(false);
                OutputUpdatedCount += 1;
            until ItemLedgerEntry.Next() = 0;


        // Process Consumption entries
        ItemLedgerEntry.Reset();
        ItemLedgerEntry.SetRange("Entry Type", ItemLedgerEntry."Entry Type"::Consumption);
        ItemLedgerEntry.SetFilter("Source Lot No. IPK", '=%1', '');

        if ItemLedgerEntry.FindSet() then
            repeat
                UpdatedCount += 1;
                Window.Update(1, StrSubstNo(ConsumptionEntryMsg, ItemLedgerEntry."Entry No."));
                Window.Update(2, UpdatedCount);

                if PopulateConsumptionSourceLotNo(ItemLedgerEntry) then
                    ConsumptionUpdatedCount += 1;
            until ItemLedgerEntry.Next() = 0;


        Window.Close();
        Message(MigrationCompleteMsg, OutputUpdatedCount, ConsumptionUpdatedCount, UpdatedCount);
    end;

    local procedure PopulateConsumptionSourceLotNo(var ConsumptionEntry: Record "Item Ledger Entry"): Boolean
    var
        OutputEntry: Record "Item Ledger Entry";
        LotNo: Code[50];
    begin
        // Strategy 1: Look for output entry AFTER consumption (Entry No. > Consumption Entry No.)
        if FindRelatedOutputByEntryNo(ConsumptionEntry, OutputEntry) then
            LotNo := OutputEntry."Lot No."
        else
            // Strategy 2: Look for output entry by Document No. and SystemCreatedAt (after consumption time)
            if FindRelatedOutputByDocumentAndTime(ConsumptionEntry, OutputEntry) then
                LotNo := OutputEntry."Lot No."
            else
                // Strategy 3: Look for any output entry after consumption in same document
                if FindRelatedOutputByDocument(ConsumptionEntry, OutputEntry) then
                    LotNo := OutputEntry."Lot No.";

        if LotNo <> '' then begin
            ConsumptionEntry."Source Lot No. IPK" := LotNo;
            ConsumptionEntry.Modify(false);
            exit(true);
        end;

        exit(false);
    end;

    local procedure FindRelatedOutputByEntryNo(ConsumptionEntry: Record "Item Ledger Entry"; var OutputEntry: Record "Item Ledger Entry"): Boolean
    var
        TestOutputEntryNo: Integer;
        MaxSearchRange: Integer;
    begin
        // Look for output entry AFTER the consumption entry (consumption feeds into output)
        // Try Entry No. + 1, + 2, + 3 etc. within reasonable range
        MaxSearchRange := 10; // Search up to 10 entries ahead

        OutputEntry.Reset();
        OutputEntry.SetRange("Entry Type", OutputEntry."Entry Type"::Output);
        OutputEntry.SetRange("Document No.", ConsumptionEntry."Document No.");
        OutputEntry.SetFilter("Lot No.", '<>%1', '');

        for TestOutputEntryNo := ConsumptionEntry."Entry No." + 1 to ConsumptionEntry."Entry No." + MaxSearchRange do begin
            OutputEntry.SetRange("Entry No.", TestOutputEntryNo);
            if OutputEntry.FindFirst() then
                exit(true);
        end;

        // If exact entry number matching fails, look for any output with higher entry number in same document
        OutputEntry.SetRange("Entry No.");
        OutputEntry.SetFilter("Entry No.", '>%1', ConsumptionEntry."Entry No.");
        OutputEntry.SetCurrentKey("Entry No.");
        exit(OutputEntry.FindFirst());
    end;

    local procedure FindRelatedOutputByDocumentAndTime(ConsumptionEntry: Record "Item Ledger Entry"; var OutputEntry: Record "Item Ledger Entry"): Boolean
    var
        BestOutputEntry: Record "Item Ledger Entry";
        TimeDifference: Duration;
        BestTimeDifference: Duration;
        FoundMatch: Boolean;
    begin
        // Look for output entries in the same document that occurred AFTER the consumption
        OutputEntry.Reset();
        OutputEntry.SetRange("Entry Type", OutputEntry."Entry Type"::Output);
        OutputEntry.SetRange("Document No.", ConsumptionEntry."Document No.");
        OutputEntry.SetRange("Posting Date", ConsumptionEntry."Posting Date");
        OutputEntry.SetFilter("Lot No.", '<>%1', '');

        BestTimeDifference := 999999999;
        FoundMatch := false;

        if OutputEntry.FindSet() then
            repeat
                // Find output entries that occurred AFTER consumption (consumption feeds into output)
                if OutputEntry.SystemCreatedAt >= ConsumptionEntry.SystemCreatedAt then begin
                    TimeDifference := OutputEntry.SystemCreatedAt - ConsumptionEntry.SystemCreatedAt;
                    if TimeDifference < BestTimeDifference then begin
                        BestTimeDifference := TimeDifference;
                        BestOutputEntry := OutputEntry;
                        FoundMatch := true;
                    end;
                end;
            until OutputEntry.Next() = 0;

        if FoundMatch then begin
            OutputEntry := BestOutputEntry;
            exit(true);
        end;

        exit(false);
    end;

    local procedure FindRelatedOutputByDocument(ConsumptionEntry: Record "Item Ledger Entry"; var OutputEntry: Record "Item Ledger Entry"): Boolean
    begin
        // Fallback: Find the first output entry AFTER this consumption entry in the same document
        OutputEntry.Reset();
        OutputEntry.SetRange("Entry Type", OutputEntry."Entry Type"::Output);
        OutputEntry.SetRange("Document No.", ConsumptionEntry."Document No.");
        OutputEntry.SetRange("Posting Date", ConsumptionEntry."Posting Date");
        OutputEntry.SetFilter("Lot No.", '<>%1', '');
        OutputEntry.SetFilter("Entry No.", '>%1', ConsumptionEntry."Entry No.");
        OutputEntry.SetCurrentKey("Entry No.");

        // If we find an output entry after the consumption, use it
        if OutputEntry.FindFirst() then
            exit(true);

        // Final fallback: Any output entry in the same document (original logic)
        OutputEntry.SetRange("Entry No.");
        exit(OutputEntry.FindFirst());
    end;

    procedure MigrateSpecificDateRange(FromDate: Date; ToDate: Date)
    var
        ItemLedgerEntry: Record "Item Ledger Entry";
        Window: Dialog;
        UpdatedCount: Integer;
        TotalCount: Integer;
        DateRangeMsg: Label 'Migrating entries from %1 to %2...', Comment = '%1=FromDate; %2=ToDate';
        DialogMsg: Label 'Processing: #1########## \Updated: #2########', Comment = '%1=CurrentEntry; %2=UpdatedCount';
    begin
        Window.Open(StrSubstNo(DateRangeMsg, FromDate, ToDate) + '\\' + DialogMsg);

        ItemLedgerEntry.SetRange("Posting Date", FromDate, ToDate);
        ItemLedgerEntry.SetFilter("Source Lot No. IPK", '=%1', '');
        TotalCount := ItemLedgerEntry.Count();

        if TotalCount = 0 then begin
            Message('No entries found in the specified date range.');
            Window.Close();
            exit;
        end;

        if not ConfirmManagement.GetResponseOrDefault(StrSubstNo(ConfirmDateRangeMsg, TotalCount, FromDate, ToDate), false) then begin
            Window.Close();
            exit;
        end;

        // Process Output entries first
        ItemLedgerEntry.SetRange("Entry Type", ItemLedgerEntry."Entry Type"::Output);
        ItemLedgerEntry.SetFilter("Lot No.", '<>%1', '');

        if ItemLedgerEntry.FindSet() then
            repeat
                UpdatedCount += 1;
                Window.Update(1, StrSubstNo(OutputEntryMsg, ItemLedgerEntry."Entry No."));
                Window.Update(2, UpdatedCount);

                ItemLedgerEntry."Source Lot No. IPK" := ItemLedgerEntry."Lot No.";
                ItemLedgerEntry.Modify(false);
            until ItemLedgerEntry.Next() = 0;

        // Process Consumption entries
        ItemLedgerEntry.SetRange("Entry Type", ItemLedgerEntry."Entry Type"::Consumption);
        ItemLedgerEntry.SetRange("Lot No."); // Remove lot no filter for consumption entries

        if ItemLedgerEntry.FindSet() then
            repeat
                UpdatedCount += 1;
                Window.Update(1, StrSubstNo(ConsumptionEntryMsg, ItemLedgerEntry."Entry No."));
                Window.Update(2, UpdatedCount);

                PopulateConsumptionSourceLotNo(ItemLedgerEntry);
            until ItemLedgerEntry.Next() = 0;

        Window.Close();
        Message('Migration completed for date range %1..%2. Total entries processed: %3', FromDate, ToDate, UpdatedCount);
    end;

    procedure ValidateMigration()
    var
        ItemLedgerEntry: Record "Item Ledger Entry";
        OutputCount: Integer;
        ConsumptionCount: Integer;
        EmptyOutputCount: Integer;
        EmptyConsumptionCount: Integer;
        ValidationMsg: Label 'Migration Validation Results:\\ Output Entries:\  - Total: %1\  - With Source Lot No.: %2\  - Missing Source Lot No.: %3\\ Consumption Entries:\  - Total: %4\  - With Source Lot No.: %5\  - Missing Source Lot No.: %6', Comment = '%1=OutputTotal; %2=OutputWithLotNo; %3=OutputMissing; %4=ConsumptionTotal; %5=ConsumptionWithLotNo; %6=ConsumptionMissing';
    begin
        // Count Output entries
        ItemLedgerEntry.Reset();
        ItemLedgerEntry.SetRange("Entry Type", ItemLedgerEntry."Entry Type"::Output);
        OutputCount := ItemLedgerEntry.Count();

        ItemLedgerEntry.SetFilter("Source Lot No. IPK", '=%1', '');
        EmptyOutputCount := ItemLedgerEntry.Count();

        // Count Consumption entries
        ItemLedgerEntry.Reset();
        ItemLedgerEntry.SetRange("Entry Type", ItemLedgerEntry."Entry Type"::Consumption);
        ConsumptionCount := ItemLedgerEntry.Count();

        ItemLedgerEntry.SetFilter("Source Lot No. IPK", '=%1', '');
        EmptyConsumptionCount := ItemLedgerEntry.Count();

        Message(ValidationMsg,
                OutputCount, OutputCount - EmptyOutputCount, EmptyOutputCount,
                ConsumptionCount, ConsumptionCount - EmptyConsumptionCount, EmptyConsumptionCount);
    end;
}
