codeunit 60002 "Ipek Sales Management IPK"
{
    //Access = Internal;
    SingleInstance = true;
    internal procedure SetSaleWarehouseShipQty(ErrorInfo: ErrorInfo)
    var
        CurrSalesLine: Record "Sales Line";
    begin
        CurrSalesLine.Get(ErrorInfo.RecordId());
        CurrSalesLine.Validate("Warehouse Qty. to Ship IPK", CurrSalesLine."Outstanding Quantity");
        CurrSalesLine.Modify(true);
    end;

    internal procedure OnAfterValidate_LabelText_LoadHeader(var LoadHeader: Record "Load Header IPK")
    var
        LoadLine: Record "Load Line IPK";
        InsufficientQuantityErr: Label 'Insufficient quantity available at the selected location. Please choose a different package to continue.';
    begin
        if LoadHeader."Label Text" = '' then
            exit;

        LoadLine.Init();
        LoadLine."Warehouse Shipment No." := LoadHeader."Warehouse Shipment No.";
        LoadLine."Document No." := LoadHeader."No.";
        LoadLine.Insert(true);
        LoadLine.Validate("Label No.", LoadHeader."Label Text");
        LoadLine.Modify(true);

        if LoadLine.Quantity = 0 then
            LoadLine.Delete(true);
        LoadLine.CalcFields("Total Read Quantity", "Remaining Quantity At Loc.");

        if LoadLine."Total Read Quantity" > LoadLine."Remaining Quantity At Loc." then
            Error(InsufficientQuantityErr);

        LoadHeader."Last Read Barcode" := LoadHeader."Label Text";
        LoadHeader."Last Read Quantity" := LoadLine.Quantity;
        LoadHeader."Label Text" := '';
    end;

    procedure ProcessLoadCardPalletBarcode(PackageNoInformation: Record "Package No. Information"; var LoadHeader: Record "Load Header IPK")
    var
        PackageNoInfoLine: Record "Package No. Info. Line IPK";
    begin
        PackageNoInfoLine.SetRange("Item No.", '');
        PackageNoInfoLine.SetRange("Variant Code", '');
        PackageNoInfoLine.SetRange("Package No.", PackageNoInformation."Package No.");
        PackageNoInfoLine.FindSet();

        repeat
            LoadHeader.Validate("Label Text", PackageNoInfoLine."Source Package No. IPK");
            LoadHeader.Modify(true);
        until PackageNoInfoLine.Next() = 0;
    end;

    internal procedure OnAfterValidate_Status_LoadHeader(var LoadHeader: Record "Load Header IPK")
    begin
        case LoadHeader.Status of
            "Load Status IPK"::"Ready-to Load":
                ReadytoLoadChecks(LoadHeader);
            "Load Status IPK"::Loading:
                ;//
            "Load Status IPK"::Loaded:
                ;//
            "Load Status IPK"::Shipped:
                ;//
        end;
    end;

    local procedure ReadytoLoadChecks(var LoadHeader: Record "Load Header IPK")
    var
        LoadPlanningLine: Record "Load Planning Line IPK";
        QuantityErr: Label 'Quantity Planned = %1 cannot be greater than Quantity = %2.', Comment = '%1="Load Planning Line IPK"."Quantity Planned"; %2="Load Planning Line IPK".Quantity';
    begin
        LoadPlanningLine.SetAutoCalcFields("Quantity Planned");
        LoadPlanningLine.SetRange("Warehouse Shipment No.", LoadHeader."Warehouse Shipment No.");
        LoadPlanningLine.SetRange("Document No.", LoadHeader."No.");
        if LoadPlanningLine.FindSet(false) then
            repeat
                if LoadPlanningLine."Quantity Planned" > LoadPlanningLine.Quantity then
                    Error(QuantityErr, LoadPlanningLine."Quantity Planned", LoadPlanningLine.Quantity);
            until LoadPlanningLine.Next() = 0;
    end;

    // procedure LoadedChecks(var LoadHeader: Record "Load Header IPK")
    // begin

    // end;

    local procedure PostWarehouseShipmentHeaderFromLoadHeader(var WarehouseShipmentHeader: Record "Warehouse Shipment Header")
    var
        PostedWhseShptHeader: Record "Posted Whse. Shipment Header";
        // TempWarehouseShipmentHeader: Record "Warehouse Shipment Header" temporary;
        WarehouseShipmentLine: Record "Warehouse Shipment Line";
        IpekSalesManagement: Codeunit "Ipek Sales Management IPK";
    //NoValidLineErr: Label newLabel;
    begin
        // TempWarehouseShipmentHeader := WarehouseShipmentHeader;

        WarehouseShipmentLine.SetRange("No.", WarehouseShipmentHeader."No.");
        WarehouseShipmentLine.SetFilter("Qty. to Ship", '>0');

        if not WarehouseShipmentLine.FindSet() then
            Error(NoValidLineErr);
        Codeunit.Run(Codeunit::"Whse.-Post Shipment (Yes/No)", WarehouseShipmentLine);

        // Message(WarehouseShipmentHeader."Last Shipping No.");

        if WarehouseShipmentHeader."Single Shipment Per Order IPK" then begin
            PostedWhseShptHeader.SetRange("Whse. Shipment No.", WarehouseShipmentHeader."No.");
            PostedWhseShptHeader.FindFirst();
            IpekSalesManagement.HandleSingleShipment(PostedWhseShptHeader);
        end;
    end;

    local procedure HandleWarehouseLineCertifications(var PostedWhseShptHeader: Record "Posted Whse. Shipment Header"; var WarehouseShipmentHeader: Record "Warehouse Shipment Header"; var WhseComment: Record "Warehouse Comment Line"; var LineNo: Integer)
    var
        ProductionBOMLine: Record "Production BOM Line";
        ItemCertificateLine: Record "Item Certificate Line IPK";
        WarehouseShipmentLine: Record "Warehouse Shipment Line";
    begin
        WarehouseShipmentLine.SetRange("No.", WarehouseShipmentHeader."No.");
        WarehouseShipmentLine.FindSet(true);
        repeat
            ProductionBOMLine.SetRange("Production BOM No.", WarehouseShipmentLine."Item No.");
            ProductionBOMLine.FindSet();
            repeat
                ItemCertificateLine.SetAutoCalcFields("Comment Line For Document");
                ItemCertificateLine.SetRange("Item No.", ProductionBOMLine."No.");
                if ItemCertificateLine.FindSet() then
                    repeat
                        WhseComment.SetRange("Table Name", WhseComment."Table Name"::"Posted Whse. Shipment");
                        WhseComment.SetRange(Type, WhseComment.Type::" ");
                        WhseComment.SetRange("No.", PostedWhseShptHeader."No.");
                        WhseComment.SetRange(Comment, 'Sertifika: ' + ItemCertificateLine."Comment Line For Document");
                        if not WhseComment.FindFirst() then
                            InsertNewCommnetLine(PostedWhseShptHeader, Format(ItemCertificateLine."Comment Line For Document"), WhseComment, LineNo, 'Sertifika: ');//Madde ölçü birimleri 
                    until ItemCertificateLine.Next() = 0;
            until ProductionBOMLine.Next() = 0;
        until WarehouseShipmentLine.Next() = 0;
    end;

    local procedure HandleInvoiceLineCertifications(var EInvoiceHeader: Record "E-Invoice Header INF"; SalesHeader: Record "Sales Header"; var LineNo: Integer)
    var
        ProductionBOMLine: Record "Production BOM Line";
        ItemCertificateLine: Record "Item Certificate Line IPK";
        SalesLine: Record "Sales Line";
        EInvoiceHeaderComment: Record "E-Invoice Header Comment INF";
    begin
        SalesLine.SetRange("Document No.", SalesHeader."No.");
        SalesLine.SetRange("Document Type", SalesHeader."Document Type");
        SalesLine.FindSet(true);
        repeat
            ProductionBOMLine.SetRange("Production BOM No.", SalesLine."No.");
            ProductionBOMLine.FindSet();
            repeat
                ItemCertificateLine.SetAutoCalcFields("Comment Line For Document");
                ItemCertificateLine.SetRange("Item No.", ProductionBOMLine."No.");
                if ItemCertificateLine.FindSet() then
                    repeat
                        EInvoiceHeaderComment.SetRange("Document Type", EInvoiceHeader."Document Type");
                        EInvoiceHeaderComment.SetRange("Document No.", EInvoiceHeader."No.");
                        EInvoiceHeaderComment.SetRange("Comment Text", 'Sertifika: ' + ItemCertificateLine."Comment Line For Document");
                        if not EInvoiceHeaderComment.FindFirst() then
                            InsertNewCommnetLineForEInvoiceHeader(EInvoiceHeader, Format(ItemCertificateLine."Comment Line For Document"), LineNo, 'Sertifika: ');//Madde ölçü birimleri 
                    until ItemCertificateLine.Next() = 0;
            until ProductionBOMLine.Next() = 0;
        until SalesLine.Next() = 0;
    end;


    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Whse.-Post Shipment", OnAfterCheckWhseShptLines, '', false, false)]
    local procedure "Whse.-Post Shipment_OnAfterCheckWhseShptLines"(var WhseShptHeader: Record "Warehouse Shipment Header"; var WhseShptLine: Record "Warehouse Shipment Line"; Invoice: Boolean; var SuppressCommit: Boolean)
    begin
        SuppressCommit := true;
    end;


    procedure WhsePostShipment_OnBeforeDeleteUpdateWhseShptLine()
    begin
        SkipDeletionOfLoad := true;
    end;

    procedure WarehouseShipmentLine_OnBeforeUpdateDocumentStatus(var WarehouseShipmentLine: Record "Warehouse Shipment Line")
    var
        LoadHeader: Record "Load Header IPK";
        DeleteAllQst: Label 'Do you want to delete all loads for this document?';
        ProcessAbortedErr: Label 'Process aborted by user.';
    begin
        if SkipDeletionOfLoad then
            exit;

        LoadHeader.SetRange("Warehouse Shipment No.", WarehouseShipmentLine."No.");
        if not LoadHeader.IsEmpty() then
            if not ConfirmManagement.GetResponseOrDefault(DeleteAllQst, true) then
                Error(ProcessAbortedErr);
        LoadHeader.DeleteAll(true);
    end;


    procedure ShipLoadHeader(var LoadHeader: Record "Load Header IPK")
    var
        WarehouseShipmentHeader: Record "Warehouse Shipment Header";
    begin
        IpekPamukSetup.GetRecordOnce();
        LoadHeader.TestField(Status, LoadHeader.Status::Loaded);

        LoadHeader.TestField("Truck License Plate");
        //LoadHeader.TestField("Trailer License Plate");
        LoadHeader.TestField("Driver Name");
        LoadHeader.TestField("Driver Surname");
        LoadHeader.TestField("Driver VAT Registration No.");

        LoadHeader.Validate("Posting Date", Today());

        WarehouseShipmentHeader.Get(LoadHeader."Warehouse Shipment No.");
        WarehouseShipmentHeader.Validate("Posting Date", Today());
        WarehouseShipmentHeader.Validate("Transport ID EHSP INF", CopyStr(LoadHeader."Truck License Plate" + '/' + LoadHeader."Trailer License Plate", 1, 20));
        WarehouseShipmentHeader.Validate("Driver Person Name EHSP INF", CopyStr(LoadHeader."Driver Name", 1, 30));
        WarehouseShipmentHeader.Validate("Driver Person Surname EHSP INF", CopyStr(LoadHeader."Driver Surname", 1, 30));
        WarehouseShipmentHeader.Validate("DriverPersonVATRegNo. EHSP INF", CopyStr(LoadHeader."Driver VAT Registration No.", 1, 30));
        WarehouseShipmentHeader.Modify(true);

        AssignLotPackageNosToWarehouseShipmentLineFromLoadHeader(LoadHeader);

        GlobalLoadHeader := LoadHeader;
        PostWarehouseShipmentHeaderFromLoadHeader(WarehouseShipmentHeader);
        Clear(GlobalLoadHeader);

        LoadHeader.Validate(Status, LoadHeader.Status::Shipped);

        //LoadHeader.Modify(true);
    end;



    procedure AssignLotPackageNosToWarehouseShipmentLineFromLoadHeader(LoadHeader: Record "Load Header IPK")
    var
        LoadLine: Record "Load Line IPK";
        WarehouseShipmentLine: Record "Warehouse Shipment Line";
        NoLinesFoundErr: Label 'No Load Line found for Warehouse Shipment No. %1 and Load No. %2.', Comment = '%1="Load Header IPK"."Warehouse Shipment No."; %2="Load Header IPK"."No."';
    begin

        LoadLine.SetRange("Warehouse Shipment No.", LoadHeader."Warehouse Shipment No.");
        LoadLine.SetRange("Document No.", LoadHeader."No.");
        if not LoadLine.FindSet(false) then
            Error(NoLinesFoundErr, LoadHeader."Warehouse Shipment No.", LoadHeader."No.");

        WarehouseShipmentLine.SetRange("No.", LoadHeader."Warehouse Shipment No.");
        // WarehouseShipmentLine.SetRange("Line No.", LoadLine."Warehouse Shipment Line No.");
        WarehouseShipmentLine.FindSet();
        // WarehouseShipmentLine.Validate("Qty. to Ship", 0);
        // WarehouseShipmentLine.ModifyAll("Qty. to Ship", 0, true);
        // WarehouseShipmentLine.Modify(true);
        repeat
            WarehouseShipmentLine.Validate("Qty. to Ship", 0);
            WarehouseShipmentLine.Modify(true);
        until WarehouseShipmentLine.Next() = 0;


        repeat

            WarehouseShipmentLine.Get(LoadHeader."Warehouse Shipment No.", LoadLine."Warehouse Shipment Line No.");
            WarehouseShipmentLine.Validate("Qty. to Ship", WarehouseShipmentLine."Qty. to Ship" + LoadLine.Quantity);

            WarehouseShipmentLine.Modify(true);

            AssignLotSerialNoToWarehouseShipmentLine(LoadHeader, WarehouseShipmentLine, LoadLine);
            UpdatePackageInfoLine(LoadLine)
        until LoadLine.Next() = 0;
    end;

    procedure UpdatePackageInfoLine(LoadLine: Record "Load Line IPK")
    var
        PackageNoInfoLine: Record "Package No. Info. Line IPK";
        PackageNoInformation: Record "Package No. Information";
    begin
        PackageNoInfoLine.SetRange("Item No.", '');
        PackageNoInfoLine.SetRange("Variant Code", '');
        PackageNoInfoLine.SetRange("Package No.", LoadLine."Label No.");
        PackageNoInfoLine.SetRange("Line Item No.", LoadLine."Item No.");
        PackageNoInfoLine.SetRange("Line Variant Code", LoadLine."Variant Code");
        PackageNoInfoLine.SetRange("Lot No.", LoadLine."Lot No.");
        PackageNoInfoLine.FindFirst();
        PackageNoInformation.Get('', '', PackageNoInfoLine."Package No.");
        if PackageNoInfoLine.Quantity = LoadLine.Quantity then begin
            PackageNoInformation.Validate("Sales Order No. IPK", LoadLine."Source No.");
            PackageNoInformation.Validate("Warehouse Shipment No. IPK", LoadLine."Warehouse Shipment No.");
            PackageNoInformation.Validate("Load No. IPK", LoadLine."Document No.");
            PackageNoInformation.Modify(true);
        end else begin
            PackageNoInfoLine.Validate(Quantity, PackageNoInfoLine.Quantity - LoadLine.Quantity);
            PackageNoInfoLine.Modify(true);
        end;

    end;

    procedure AssignLotSerialNoToWarehouseShipmentLine(LoadHeader: Record "Load Header IPK"; WarehouseShipmentLine: Record "Warehouse Shipment Line"; LoadLine: Record "Load Line IPK")
    var
        Item: Record Item;
        ItemTrackingCode: Record "Item Tracking Code";
        // PackageNoInformation: Record "Package No. Information";
        SalesLine: Record "Sales Line";
        TempSourceTrackingSpecification: Record "Tracking Specification" temporary;
        TempTrackingSpecification: Record "Tracking Specification" temporary;
        WarehouseShipmentHeader: Record "Warehouse Shipment Header";
        SalesLineReserve: Codeunit "Sales Line-Reserve";
        ItemTrackingLines: Page "Item Tracking Lines";

    begin
        WarehouseShipmentHeader.Get(LoadHeader."Warehouse Shipment No.");
        Item.Get(WarehouseShipmentLine."Item No.");
        ItemTrackingCode.Get(Item."Item Tracking Code");

        SalesLine.Get(WarehouseShipmentLine."Source Subtype", WarehouseShipmentLine."Source No.", WarehouseShipmentLine."Source Line No.");
        SalesLineReserve.InitFromSalesLine(TempSourceTrackingSpecification, SalesLine);

        TempTrackingSpecification.Init();
        TempTrackingSpecification.SetItemData(WarehouseShipmentLine."Item No.", WarehouseShipmentLine.Description, WarehouseShipmentLine."Location Code", WarehouseShipmentLine."Variant Code", WarehouseShipmentLine."Bin Code", WarehouseShipmentLine."Qty. per Unit of Measure");

        TempTrackingSpecification."Lot No." := LoadLine."Lot No.";

        //
        // TempTrackingSpecification.Reset();
        // TempTrackingSpecification.SetTrackingKey();
        // TempTrackingSpecification.SetTrackingFilterFromSpec(TempTrackingSpecification);
        // if TempTrackingSpecification.FindFirst() then
        //     HandledQty := WarehouseShipmentLine."Qty. Shipped (Base)"
        // else
        //     HandledQty := 0;
        //

        TempTrackingSpecification.SetQuantities(LoadLine.Quantity, //QtyBase
                                                LoadLine.Quantity,                                                   //QtyToHandle
                                                LoadLine.Quantity * WarehouseShipmentLine."Qty. per Unit of Measure", //QtyToHandleBase    
                                                0,                                                                      //QtyToInvoice
                                                0,                                                                      //QtyToInvoiceBase
                                                WarehouseShipmentLine."Qty. Shipped (Base)",                             //QtyHandledBase            
                                                0);                                                                       //QtyInvoicedBase        

        TempTrackingSpecification.Insert(false);

        // if (WarehouseShipmentLine."Qty. Shipped" = 0) then
        ItemTrackingLines.SetBlockCommit(true);

        ItemTrackingLines.RegisterItemTrackingLines(TempSourceTrackingSpecification, WorkDate(), TempTrackingSpecification);
        // ItemTrackingLines.SetBlockCommit(false);

    end;

    [EventSubscriber(ObjectType::Page, Page::"Item Tracking Lines", OnRegisterItemTrackingLinesOnBeforeFind, '', false, false)]
    local procedure "Item Tracking Lines_OnRegisterItemTrackingLinesOnBeforeFind"(var TrackingSpecification: Record "Tracking Specification"; var TempTrackingSpecification: Record "Tracking Specification" temporary; CurrentRunMode: Enum "Item Tracking Run Mode")
    begin
        if not TrackingSpecification.Find('-') then
            NewLot := true
        else
            NewLot := false;

    end;

    [EventSubscriber(ObjectType::Page, Page::"Item Tracking Lines", OnAfterCopyTrackingSpec, '', false, false)]
    local procedure "Item Tracking Lines_OnAfterCopyTrackingSpec"(var SourceTrackingSpec: Record "Tracking Specification"; var DestTrkgSpec: Record "Tracking Specification")
    begin
        if NewLot then begin
            SourceTrackingSpec."Quantity Handled (Base)" := 0;
            DestTrkgSpec."Quantity Handled (Base)" := 0;
        end;
    end;

    procedure CalculateFOBUnitPriceFromSalesHeader(var SalesHeader: Record "Sales Header")
    var
        SalesLine: Record "Sales Line";
        FreightAmountFactor: Decimal;
        LineFobAmount: Decimal;
        LineFreightAmount: Decimal;
        TotalLineAmount: Decimal;
    begin
        //SalesHeader.TestField("Freight Amount IPK");

        SalesLine.SetRange("Document Type", SalesHeader."Document Type");
        SalesLine.SetRange("Document No.", SalesHeader."No.");
        SalesLine.SetRange(Type, SalesLine.Type::Item);
        SalesLine.CalcSums("Line Amount");

        TotalLineAmount := SalesLine."Line Amount";
        FreightAmountFactor := SalesHeader."Freight Amount IPK" / TotalLineAmount;

        SalesLine.FindSet(true);
        repeat
            LineFreightAmount := FreightAmountFactor * SalesLine."Line Amount";
            LineFobAmount := SalesLine."Line Amount" - LineFreightAmount;
            SalesLine.Validate("FOB Piece Unit Price IPK", LineFobAmount / SalesLine."Line Unit Quantity IPK");
            SalesLine.Modify(true);
        until SalesLine.Next() = 0;
    end;

    procedure InsertFreightItemChargeLineFromSalesHeader(var SalesHeader: Record "Sales Header")
    var
        SalesLine: Record "Sales Line";
        FreightAmount: Decimal;
        SalesLineNo: Integer;
        FreightChargeErr: Label 'You cannot calculate freight item charge without any freight amount';
        FreightItemChargeLineInsertedMsg: Label 'Freight Item Charge Line inserted.';
    begin
        IpekPamukSetup.GetRecordOnce();
        IpekPamukSetup.TestField("Freight Item Charge No.");

        SalesLine.SetRange("Document Type", SalesHeader."Document Type");
        SalesLine.SetRange("Document No.", SalesHeader."No.");
        SalesLine.SetRange(Type, SalesLine.Type::Item);
        SalesLine.CalcSums("FOB Line Amount IPK", Amount);

        FreightAmount := -SalesLine."FOB Line Amount IPK" + SalesLine.Amount;
        SalesLine.FindLast();

        SalesLineNo := SalesLine."Line No." + 10000;

        if FreightAmount > 0 then begin
            SalesLine.Init();
            SalesLine."Document Type" := SalesHeader."Document Type";
            SalesLine."Document No." := SalesHeader."No.";
            SalesLine."Line No." := SalesLineNo;
            SalesLine.Insert(true);
            SalesLine.Validate("Type", SalesLine.Type::"Charge (Item)");
            SalesLine.Validate("No.", IpekPamukSetup."Freight Item Charge No.");

            SalesLine.Validate("Quantity", 1);
            SalesLine.Validate("Q/O Unit Price INF", FreightAmount);//"Q/O Unit Price INF"
            SalesLine.Validate("Tax Disc. Charge Line Type INF", SalesLine."Tax Disc. Charge Line Type INF"::"Transport (Export)");
            SalesLine.Validate("Goods and Srv. Line No. INF", SalesLineNo - 10000);
            SalesLine.Modify(true);

            Message(FreightItemChargeLineInsertedMsg);
        end else
            Error(FreightChargeErr);
    end;

    procedure WhsePostShipment_OnBeforePostedWhseShptHeaderInsertFunction(var PostedWhseShipmentHeader: Record "Posted Whse. Shipment Header")
    begin
        if GlobalLoadHeader."Warehouse Shipment No." <> '' then begin
            GlobalLoadHeader."Posting Date" := Today();
            GlobalLoadHeader."Posted Document No." := PostedWhseShipmentHeader."No.";
            GlobalLoadHeader."E-Shipment No." := PostedWhseShipmentHeader."No.";
            GlobalLoadHeader.Validate(Status, GlobalLoadHeader.Status::Shipped);
            GlobalLoadHeader.Modify(true);
        end;
    end;

    procedure UpdateQuantities(var WarehouseShipmentLine: Record "Warehouse Shipment Line"; var SalesLine: Record "Sales Line")
    begin
        WarehouseShipmentLine.Validate(Quantity, SalesLine."Warehouse Qty. to Ship IPK");

        WarehouseShipmentLine.Validate("Unit Volume IPK", SalesLine."Unit Volume");
        WarehouseShipmentLine.Validate("Net Weight IPK", SalesLine."Net Weight");
        WarehouseShipmentLine.Validate("Gross Weight IPK", SalesLine."Gross Weight");
        WarehouseShipmentLine.Validate("Units per Parcel IPK", SalesLine."Units per Parcel");
        WarehouseShipmentLine.Validate("Item Reference No. IPK", SalesLine."Item Reference No.");

        WarehouseShipmentLine.Validate("Qty. to Ship", 0);

        SalesLine.Validate("Warehouse Qty. to Ship IPK", 0);
        SalesLine.Modify(true);
    end;

    procedure PrepareForSingleShipment(WarehouseShipmentHeader: Record "Warehouse Shipment Header")
    var
        LoadHeader: Record "Load Header IPK";
        LoadPlanningLine: Record "Load Planning Line IPK";
        WarehouseShipmentLine: Record "Warehouse Shipment Line";
        AlreadyLoadCardOpenedErr: Label 'You need to delete Load No.: %1 before proceeding.', Comment = '%1="Load Header IPK"."No."';
        SuccesMsg: Label 'Load No.: %1 is successfully created.', Comment = '%1="Load Header IPK"."No."';
    begin
        LoadHeader.SetRange("Warehouse Shipment No.", WarehouseShipmentHeader."No.");
        if LoadHeader.FindFirst() then
            Error(AlreadyLoadCardOpenedErr, LoadHeader."No.");

        LoadHeader.Init();
        LoadHeader."Warehouse Shipment No." := WarehouseShipmentHeader."No.";
        LoadHeader.Insert(true);
        LoadHeader.Validate("Load Method", LoadHeader."Load Method"::Truck);
        LoadHeader.Validate(Mix, true);
        LoadHeader.Modify(true);

        WarehouseShipmentLine.SetRange("No.", WarehouseShipmentHeader."No.");
        if not WarehouseShipmentLine.FindSet(false) then
            Error(NoValidLineErr);

        repeat
            LoadPlanningLine.Init();
            LoadPlanningLine."Document No." := LoadHeader."No.";
            LoadPlanningLine."Warehouse Shipment No." := LoadHeader."Warehouse Shipment No.";
            LoadPlanningLine.Insert(true);
            LoadPlanningLine.Validate("Warehouse Shipment Line No.", WarehouseShipmentLine."Line No.");
            LoadPlanningLine.Validate("Quantity to Load", WarehouseShipmentLine.Quantity);
            LoadPlanningLine.Modify(true);
        until WarehouseShipmentLine.Next() = 0;

        Message(SuccesMsg, LoadHeader."No.");
    end;
    // procedure GetGlobalLoadHeader(): Record "Load Header IPK"
    // begin 

    // end;
    procedure AssignValuesToPostedWarehouseShipmentHeader(var PostedWhseShptHeader: Record "Posted Whse. Shipment Header"; var WarehouseShipmentHeader: Record "Warehouse Shipment Header")
    begin
        PostedWhseShptHeader."Vessel Name IPK" := WarehouseShipmentHeader."Vessel Name IPK";
        PostedWhseShptHeader."Voyage No. IPK" := WarehouseShipmentHeader."Voyage No. IPK";
        PostedWhseShptHeader."PoL Code IPK" := WarehouseShipmentHeader."PoL Code IPK";
        PostedWhseShptHeader."PoA Code IPK" := WarehouseShipmentHeader."PoA Code IPK";
        PostedWhseShptHeader."Line IPK" := WarehouseShipmentHeader."Line IPK";
        PostedWhseShptHeader."Forwarder IPK" := WarehouseShipmentHeader."Forwarder IPK";
        PostedWhseShptHeader."ETS IPK" := WarehouseShipmentHeader."ETS IPK";
        PostedWhseShptHeader."ETA IPK" := WarehouseShipmentHeader."ETA IPK";
        PostedWhseShptHeader."Cutt-off Date IPK" := WarehouseShipmentHeader."Cutt-off Date IPK";
        PostedWhseShptHeader."Export No. IPK" := WarehouseShipmentHeader."Export No. IPK";
        PostedWhseShptHeader."Customs Code IPK" := WarehouseShipmentHeader."Customs Code IPK";
        PostedWhseShptHeader."Customs Officer IPK" := WarehouseShipmentHeader."Customs Officer IPK";
        PostedWhseShptHeader."Flag IPK" := WarehouseShipmentHeader."Flag IPK";
        PostedWhseShptHeader."Vessel Shipping Agent Code IPK" := WarehouseShipmentHeader."Vessel Shipping Agent Code IPK";
        PostedWhseShptHeader."Single Shipment Per Order IPK" := WarehouseShipmentHeader."Single Shipment Per Order IPK";
        PostedWhseShptHeader."Priority Shipment IPK" := WarehouseShipmentHeader."Priority Shipment IPK";
    end;

    procedure HandleSingleShipment(var PostedWhseShptHeader: Record "Posted Whse. Shipment Header")
    var
        PostedWhseShipmentLine: Record "Posted Whse. Shipment Line";
        SalesHeader: Record "Sales Header";
        SalesLine: Record "Sales Line";
        WarehouseShipmentHeader: Record "Warehouse Shipment Header";
        ReleaseWhseShptDoc: Codeunit "Whse.-Shipment Release";
    begin

        if WarehouseShipmentHeader.Get(PostedWhseShptHeader."Whse. Shipment No.") then begin
            ReleaseWhseShptDoc.Reopen(WarehouseShipmentHeader);
            WarehouseShipmentHeader.Delete(true)
        end;

        PostedWhseShipmentLine.SetRange("No.", PostedWhseShptHeader."No.");
        PostedWhseShipmentLine.FindFirst();

        // SalesHeader.Get(SalesHeader."Document Type"::Order, PostedWhseShipmentLine."Source No.");
        // SalesHeader.Reset();

        SalesHeader.SetRange("Document Type", SalesHeader."Document Type"::Order);
        SalesHeader.SetRange("No.", PostedWhseShipmentLine."Source No.");
        SalesHeader.FindFirst();
        SalesHeader.PerformManualReopen(SalesHeader);


        SalesLine.SetRange("Document Type", SalesLine."Document Type"::Order);
        SalesLine.SetRange("Document No.", SalesHeader."No.");

        SalesLine.FindSet(true);
        repeat
            PostedWhseShipmentLine.SetRange("No.", PostedWhseShptHeader."No.");
            PostedWhseShipmentLine.SetRange("Source No.", SalesLine."Document No.");
            PostedWhseShipmentLine.SetRange("Source Line No.", SalesLine."Line No.");
            if not PostedWhseShipmentLine.FindFirst() then
                SalesLine.Validate(Quantity, 0)
            else
                if SalesLine.Quantity <> PostedWhseShipmentLine.Quantity then
                    SalesLine.Validate(Quantity, PostedWhseShipmentLine.Quantity);
            SalesLine.Modify(true);
        until SalesLine.Next() = 0;

        SalesHeader.PerformManualRelease(SalesHeader);



    end;

    procedure InsertNewCommnetLine(var PostedWhseShptHeader: Record "Posted Whse. Shipment Header"; CommentTxt: Text[100]; var WhseComment: Record "Warehouse Comment Line"; var LineNo: Integer; CommentPrefix: Text[100])
    begin
        WhseComment.Init();
        WhseComment."Table Name" := WhseComment."Table Name"::"Posted Whse. Shipment";
        WhseComment.Type := WhseComment.Type::" ";
        WhseComment."No." := PostedWhseShptHeader."No.";
        WhseComment."Line No." := LineNo;
        WhseComment.Comment := CopyStr(CommentPrefix + CommentTxt, 1, MaxStrLen(WhseComment.Comment));
        WhseComment.Insert(true);
        LineNo += 10000;
    end;

    procedure InsertNewCommnetLineForEInvoiceHeader(var EInvoiceHeader: Record "E-Invoice Header INF"; CommentTxt: Text[100]/*; var EInvoiceHeaderComment: Record "E-Invoice Header Comment INF"*/; var LineNo: Integer; CommentPrefix: Text[100])
    var
        EInvoiceHeaderComment: Record "E-Invoice Header Comment INF";
    begin
        EInvoiceHeaderComment.Init();
        EInvoiceHeaderComment."Document Type" := EInvoiceHeader."Document Type";
        EInvoiceHeaderComment."Document No." := EInvoiceHeader."No.";
        EInvoiceHeaderComment."Comment Line No." := LineNo;
        EInvoiceHeaderComment."Comment Text" := CopyStr(CommentPrefix + CommentTxt, 1, MaxStrLen(EInvoiceHeaderComment."Comment Text"));
        EInvoiceHeaderComment.Insert(true);
        LineNo += 10000;
    end;

    // procedure AddCommentsToPostedWarehouseDocument(var PostedWhseShptHeader: Record "Posted Whse. Shipment Header"; var WarehouseShipmentHeader: Record "Warehouse Shipment Header")
    // var
    //     Customer: Record Customer;
    //     CustomerBankAccount: Record "Customer Bank Account";
    //     LoadHeader: Record "Load Header IPK";
    //     WhseComment: Record "Warehouse Comment Line";
    //     LineNo: Integer;
    // begin
    //     Customer.Get(WarehouseShipmentHeader."Sell-to Source No. INF");
    //     if CustomerBankAccount.Get(Customer."No.", Customer."Preferred Bank Account Code") then;//
    //     LoadHeader := GlobalLoadHeader;

    //     WhseComment.SetRange("Table Name", WhseComment."Table Name"::"Posted Whse. Shipment");
    //     WhseComment.SetRange(Type, WhseComment.Type::" ");
    //     WhseComment.SetRange("No.", PostedWhseShptHeader."No.");
    //     if WhseComment.FindLast() then
    //         LineNo := WhseComment."Line No." + 10000
    //     else
    //         LineNo := 10000;
    //     //InsertNewCommnetLine(PostedWhseShptHeader, WarehouseShipmentHeader."Sell-to Customer Name INF", WhseComment, LineNo, 'Fatura Yeri: ');// MK Maddesinin özelliği kağıt
    //     InsertNewCommnetLine(PostedWhseShptHeader, WarehouseShipmentHeader."Sell-to Customer Name INF", WhseComment, LineNo, 'Fatura Yeri: ');//Fatura Yeri : KI TRADE LTD.
    //     InsertNewCommnetLine(PostedWhseShptHeader, WarehouseShipmentHeader."Ship-to Name INF", WhseComment, LineNo, 'Sevk Yeri: ');//Fatura Yeri : KI TRADE LTD.
    //     //Gümrüğü :
    //     InsertNewCommnetLine(PostedWhseShptHeader, WarehouseShipmentHeader."Shipment Method Code", WhseComment, LineNo, 'Teslim Şekli: ');// Ambar sevk irsaliyesinde  sevkiyat sekmesinde "Sevkiyat yöntemi kodu"
    //     InsertNewCommnetLine(PostedWhseShptHeader, LoadHeader."Load Location", WhseComment, LineNo, 'Yükleme Yeri: ');//  Yükleme kartında "yükleme konumu" olabilir
    //     InsertNewCommnetLine(PostedWhseShptHeader, CustomerBankAccount.Name, WhseComment, LineNo, 'İhracatçı Banka: ');// Müşteri kartında Banka hesapları
    //     InsertNewCommnetLine(PostedWhseShptHeader, Customer."Payment Method Code", WhseComment, LineNo, 'Ödeme Yöntemi: ');//Müşteri kartında ödeme yöntemi kodu
    //     //Nakliyeci :
    //     InsertNewCommnetLine(PostedWhseShptHeader, '', WhseComment, LineNo, 'Araç No: ');// YÜK KARTINDA
    //     InsertNewCommnetLine(PostedWhseShptHeader, LoadHeader."Domestic Shipping Agent Code", WhseComment, LineNo, 'Nakliye Firması: ');// FORWARDER
    //     InsertNewCommnetLine(PostedWhseShptHeader, LoadHeader."Container No.", WhseComment, LineNo, 'Konteynır Numarası: ');// YÜK KARTINDA//Alper Not
    //     InsertNewCommnetLine(PostedWhseShptHeader, LoadHeader."Seal No.", WhseComment, LineNo, 'Mühür Numarası: ');// YÜK KARTINDA
    //     InsertNewCommnetLine(PostedWhseShptHeader, LoadHeader."Truck License Plate", WhseComment, LineNo, 'İç Nakliye Araç Plaka 1: ');
    //     InsertNewCommnetLine(PostedWhseShptHeader, LoadHeader."Trailer License Plate", WhseComment, LineNo, 'İç Nakliye Araç Plaka 2: ');
    //     InsertNewCommnetLine(PostedWhseShptHeader, WarehouseShipmentHeader."Vessel Shipping Agent Code IPK", WhseComment, LineNo, 'Gemi Acentesi: ');// LİNE
    //     InsertNewCommnetLine(PostedWhseShptHeader, WarehouseShipmentHeader."Vessel Name IPK", WhseComment, LineNo, 'Gemi Adı: ');//ambar sevk irs.
    //     InsertNewCommnetLine(PostedWhseShptHeader, WarehouseShipmentHeader."Voyage No. IPK", WhseComment, LineNo, 'Sefer No: ');//ambar sevk irs.
    //     InsertNewCommnetLine(PostedWhseShptHeader, WarehouseShipmentHeader."PoL Code IPK", WhseComment, LineNo, 'Kalkış Limanı: ');//Ambar sevk irs.
    //     InsertNewCommnetLine(PostedWhseShptHeader, WarehouseShipmentHeader."PoA Code IPK", WhseComment, LineNo, 'Varış Limanı: ');//Ambar sevk irs.
    //     InsertNewCommnetLine(PostedWhseShptHeader, WarehouseShipmentHeader."Flag IPK", WhseComment, LineNo, 'Bayrak: ');//Ambar sevk irs.
    //     InsertNewCommnetLine(PostedWhseShptHeader, Format(WarehouseShipmentHeader."Cutt-off Date IPK"), WhseComment, LineNo, 'Cut Off: ');// Ambar sevk irs.
    //     //Not :
    //     InsertNewCommnetLine(PostedWhseShptHeader, Format(WarehouseShipmentHeader."Total Unit Quantity IPK"), WhseComment, LineNo, 'Ölçü Birimi: ');//Madde ölçü birimleri 
    //     //InsertNewCommnetLine();
    // end;

    procedure SetDocumentCurrencyCode(var SalesInvoiceHeader: Record "Sales Invoice Header"; var EInvoiceHeader: Record "E-Invoice Header INF")
    var
        EInvoiceSetup: Record "E-Invoice Setup INF";
    begin
        EInvoiceSetup.Get();
        if SalesInvoiceHeader."Currency Code" = '' then
            EInvoiceHeader."Document Currency Code" := EInvoiceSetup."E-Invoice LCY Code"
        else
            EInvoiceHeader."Document Currency Code" := SalesInvoiceHeader."Currency Code";

        EInvoiceHeader."Pricing Currency Code" := EInvoiceSetup."E-Invoice LCY Code";
        EInvoiceHeader."Currency Exchange Rate" := SalesInvoiceHeader."Currency Factor";
        if EInvoiceHeader."Currency Exchange Rate" = 0 then
            EInvoiceHeader."Currency Exchange Rate" := 1
        else
            EInvoiceHeader."Currency Exchange Rate" := 1 / EInvoiceHeader."Currency Exchange Rate";

        EInvoiceHeader.Modify(true);
    end;

    procedure AddCommentsToEInvoice(var EInvoiceHeader: Record "E-Invoice Header INF")
    var
        Customer: Record Customer;
        EInvoiceHeaderComment: Record "E-Invoice Header Comment INF";
        SalesHeader: Record "Sales Header";
        SalesLine: Record "Sales Line";
        ShipmentMethod: Record "Shipment Method";
        LineNo: Integer;
    begin
        if not SalesHeader.Get(SalesHeader."Document Type"::Invoice, EInvoiceHeader."No.") then
            exit;

        Customer.Get(SalesHeader."Bill-to Customer No.");
        if ShipmentMethod.Get(Customer."Shipment Method Code") then;//
        SalesLine.SetRange("Document No.", SalesHeader."No.");
        SalesLine.SetRange("Document Type", SalesHeader."Document Type");
        SalesLine.FindSet();
        EInvoiceHeaderComment.SetRange("Document No.", EInvoiceHeader."No.");
        EInvoiceHeaderComment.SetRange("Document Type", EInvoiceHeader."Document Type");
        if EInvoiceHeaderComment.FindLast() then
            LineNo := EInvoiceHeaderComment."Comment Line No." + 10000
        else
            LineNo := 10000;
        if SalesHeader."Freight Amount IPK" > 0 then begin
            SalesLine.CalcSums("Q/O Line Amount INF");
            InsertNewCommnetLineForEInvoiceHeader(EInvoiceHeader, Format(SalesLine."Q/O Line Amount INF" - SalesHeader."Freight Amount IPK") + ' ' + SalesHeader."Currency Code", LineNo, 'Toplam FOB Tutar:  ');
        end;



    end;

    procedure AddCommentsToEExport(var EInvoiceHeader: Record "E-Invoice Header INF")
    var
        Customer: Record Customer;
        CustomerBankAccount: Record "Customer Bank Account";
        EInvoiceHeaderComment: Record "E-Invoice Header Comment INF";
        PaymentMethod: Record "Payment Method";
        SalesHeader: Record "Sales Header";
        ShippingAgent: Record "Shipping Agent";
        LineNo: Integer;
    begin
        if not SalesHeader.Get(SalesHeader."Document Type"::Invoice, EInvoiceHeader."No.") then
            exit;


        SalesHeader.CalcFields(Amount, "Total Gross Weight IPK", "Total Net Weight IPK", "Total Pack IPK");

        if ShippingAgent.Get(SalesHeader."Shipping Agent Code") then;//

        Customer.Get(SalesHeader."Bill-to Customer No.");
        CustomerBankAccount.SetRange("Customer No.", Customer."No.");
        CustomerBankAccount.FindFirst();

        PaymentMethod.Get(SalesHeader."Payment Method Code");


        EInvoiceHeaderComment.SetRange("Document No.", EInvoiceHeader."No.");
        EInvoiceHeaderComment.SetRange("Document Type", EInvoiceHeader."Document Type");
        if EInvoiceHeaderComment.FindLast() then
            LineNo := EInvoiceHeaderComment."Comment Line No." + 10000
        else
            LineNo := 10000;
        // if (SalesHeader."Shipment Method Code" = 'DAP') or (SalesHeader."Shipment Method Code" = 'CFR') then
        //     InsertNewCommnetLineForEInvoiceHeader(EInvoiceHeader, Format(SalesHeader.Amount - SalesHeader."Freight Amount IPK"),/* EInvoiceHeaderComment,*/ LineNo, 'Toplam FOB Tutar:  ');// MK Maddesinin özelliği kağıt
        InsertNewCommnetLineForEInvoiceHeader(EInvoiceHeader, SalesHeader."Shipment Method Code" + ' ' + SalesHeader."Transaction Specification",/* EInvoiceHeaderComment,*/ LineNo, 'Teslim Şekli: ');//
        if SalesHeader."Shipment Method Code" <> '' then
            InsertNewCommnetLineForEInvoiceHeader(EInvoiceHeader, SalesHeader."Currency Code",/* EInvoiceHeaderComment,*/ LineNo, 'Para Birimi: ');//
        if SalesHeader."Currency Factor" <> 0 then
            InsertNewCommnetLineForEInvoiceHeader(EInvoiceHeader, Format(1 / SalesHeader."Currency Factor", 0, '<Precision,4:4><Standard Format,0>'),/* EInvoiceHeaderComment,*/ LineNo, 'Döviz Kuru: ');//

        InsertNewCommnetLineForEInvoiceHeader(EInvoiceHeader, Format(SalesHeader."Export No. IPK"),/* EInvoiceHeaderComment,*/ LineNo, 'İhracat Dosya No.: ');//
        InsertNewCommnetLineForEInvoiceHeader(EInvoiceHeader, 'Mallarımız Türk Menşeilidir.',/* EInvoiceHeaderComment,*/ LineNo, 'Menşei: ');//
        InsertNewCommnetLineForEInvoiceHeader(EInvoiceHeader, 'İpek İdrofil Pamuk Sanayi ve Tic. A.Ş.',/* EInvoiceHeaderComment,*/ LineNo, 'İmalatçı: ');//
        InsertNewCommnetLineForEInvoiceHeader(EInvoiceHeader, ShippingAgent.Name,/* EInvoiceHeaderComment,*/ LineNo, 'Nakliyeci: ');//
        InsertNewCommnetLineForEInvoiceHeader(EInvoiceHeader, CustomerBankAccount.Name,/* EInvoiceHeaderComment,*/ LineNo, 'Aracı Banka: ');//
        InsertNewCommnetLineForEInvoiceHeader(EInvoiceHeader, PaymentMethod.Description,/* EInvoiceHeaderComment,*/ LineNo, 'Ödeme Şekli: ');//
        InsertNewCommnetLineForEInvoiceHeader(EInvoiceHeader, Format(SalesHeader."Total Gross Weight IPK"),/* EInvoiceHeaderComment,*/ LineNo, 'Brüt KG: ');//
        InsertNewCommnetLineForEInvoiceHeader(EInvoiceHeader, Format(SalesHeader."Total Net Weight IPK"),/* EInvoiceHeaderComment,*/ LineNo, 'Net KG : ');//
        InsertNewCommnetLineForEInvoiceHeader(EInvoiceHeader, Format(SalesHeader."Total Pack IPK"),/* EInvoiceHeaderComment,*/ LineNo, 'Kap Adet: ');//;

        HandleInvoiceLineCertifications(EInvoiceHeader, SalesHeader, LineNo);
    end;

    procedure MakeNecessaryEditionsToDomesticEShipment(var PostedWhseShipmentHeader: Record "Posted Whse. Shipment Header"; var EShipmentHeader: Record "E-Shipment Header INF")
    var
        EInvoiceSetup: Record "E-Invoice Setup INF";
        EShipmentHeaderComment: Record "E-Shipment Header Comment INF";
        EShipmentSetup: Record "E-Shipment Setup INF";
        SalesHeader: Record "Sales Header";
        WhseComment: Record "Warehouse Comment Line";
        LineNo: Integer;
    begin
        EShipmentSetup.Get();
        EInvoiceSetup.Get();
        if SalesHeader.Get(SalesHeader."Document Type"::Order, PostedWhseShipmentHeader."Source No. INF") then
            exit;
        if (SalesHeader."E-Invoice Profile ID INF" = EInvoiceSetup."E-Invoice Profile for Export") then
            exit;



        if (SalesHeader."Your Reference" <> '') then
            EShipmentHeader."Order ID" := SalesHeader."Your Reference";

        EShipmentHeaderComment.SetRange("Document No.", EShipmentHeader."No.");
        EShipmentHeaderComment.SetRange("Document Type", EShipmentHeader."Document Type");
        if EShipmentHeaderComment.FindLast() then
            LineNo := EShipmentHeaderComment."Comment Line No." + 10000
        else
            LineNo := 10000;
        InsertNewCommnetLine(PostedWhseShipmentHeader, Format(SalesHeader."No."), WhseComment, LineNo, 'Order No: ');// Salesheaderorderno;
    end;

    procedure AddCommentsToExportEShipmentHeader(var PostedWhseShptHeader: Record "Posted Whse. Shipment Header"; var WarehouseShipmentHeader: Record "Warehouse Shipment Header")
    var
        Customer: Record Customer;
        CustomerBankAccount: Record "Customer Bank Account";
        EInvoiceSetup: Record "E-Invoice Setup INF";
        LoadHeader: Record "Load Header IPK";
        WhseComment: Record "Warehouse Comment Line";
        LineNo: Integer;
    begin
        EInvoiceSetup.Get();
        Customer.Get(WarehouseShipmentHeader."Sell-to Source No. INF");
        if Customer."E-Invoice Profile ID INF" <> EInvoiceSetup."E-Invoice Profile for Export" then
            exit;

        if CustomerBankAccount.Get(Customer."No.", Customer."Preferred Bank Account Code") then;//

        LoadHeader := GlobalLoadHeader;
        WhseComment.SetRange("Table Name", WhseComment."Table Name"::"Posted Whse. Shipment");
        WhseComment.SetRange(Type, WhseComment.Type::" ");
        WhseComment.SetRange("No.", PostedWhseShptHeader."No.");
        if WhseComment.FindLast() then
            LineNo := WhseComment."Line No." + 10000
        else
            LineNo := 10000;

        //InsertNewCommnetLine(PostedWhseShptHeader, WarehouseShipmentHeader."Sell-to Customer Name INF", WhseComment, LineNo, 'Fatura Yeri: ');// MK Maddesinin özelliği kağıt
        InsertNewCommnetLine(PostedWhseShptHeader, WarehouseShipmentHeader."Sell-to Customer Name INF", WhseComment, LineNo, 'Fatura Yeri: ');//Fatura Yeri : KI TRADE LTD.
        InsertNewCommnetLine(PostedWhseShptHeader, WarehouseShipmentHeader."Ship-to Name INF", WhseComment, LineNo, 'Sevk Yeri: ');//Fatura Yeri : KI TRADE LTD.
        //Gümrüğü :
        InsertNewCommnetLine(PostedWhseShptHeader, WarehouseShipmentHeader."Shipment Method Code", WhseComment, LineNo, 'Teslim Şekli: ');// Ambar sevk irsaliyesinde  sevkiyat sekmesinde "Sevkiyat yöntemi kodu"
        InsertNewCommnetLine(PostedWhseShptHeader, LoadHeader."Load Location", WhseComment, LineNo, 'Yükleme Yeri: ');//  Yükleme kartında "yükleme konumu" olabilir
        InsertNewCommnetLine(PostedWhseShptHeader, CustomerBankAccount.Name, WhseComment, LineNo, 'İhracatçı Banka: ');// Müşteri kartında Banka hesapları
        InsertNewCommnetLine(PostedWhseShptHeader, Customer."Payment Method Code", WhseComment, LineNo, 'Ödeme Yöntemi: ');//Müşteri kartında ödeme yöntemi kodu
        //Nakliyeci :
        InsertNewCommnetLine(PostedWhseShptHeader, '', WhseComment, LineNo, 'Araç No: ');// YÜK KARTINDA
        InsertNewCommnetLine(PostedWhseShptHeader, LoadHeader."Domestic Shipping Agent Code", WhseComment, LineNo, 'Nakliye Firması: ');// FORWARDER
        InsertNewCommnetLine(PostedWhseShptHeader, LoadHeader."Container No.", WhseComment, LineNo, 'Konteynır Numarası: ');// YÜK KARTINDA
        InsertNewCommnetLine(PostedWhseShptHeader, LoadHeader."Seal No.", WhseComment, LineNo, 'Mühür Numarası: ');// YÜK KARTINDA
        InsertNewCommnetLine(PostedWhseShptHeader, LoadHeader."Truck License Plate", WhseComment, LineNo, 'İç Nakliye Araç Plaka 1: ');
        InsertNewCommnetLine(PostedWhseShptHeader, LoadHeader."Trailer License Plate", WhseComment, LineNo, 'İç Nakliye Araç Plaka 2: ');
        InsertNewCommnetLine(PostedWhseShptHeader, WarehouseShipmentHeader."Vessel Shipping Agent Code IPK", WhseComment, LineNo, 'Gemi Acentesi: ');// LİNE
        InsertNewCommnetLine(PostedWhseShptHeader, WarehouseShipmentHeader."Vessel Name IPK", WhseComment, LineNo, 'Gemi Adı: ');//ambar sevk irs.
        InsertNewCommnetLine(PostedWhseShptHeader, WarehouseShipmentHeader."Voyage No. IPK", WhseComment, LineNo, 'Sefer No: ');//ambar sevk irs.
        InsertNewCommnetLine(PostedWhseShptHeader, WarehouseShipmentHeader."PoL Code IPK", WhseComment, LineNo, 'Kalkış Limanı: ');//Ambar sevk irs.
        InsertNewCommnetLine(PostedWhseShptHeader, WarehouseShipmentHeader."PoA Code IPK", WhseComment, LineNo, 'Varış Limanı: ');//Ambar sevk irs.
        InsertNewCommnetLine(PostedWhseShptHeader, WarehouseShipmentHeader."Flag IPK", WhseComment, LineNo, 'Bayrak: ');//Ambar sevk irs.
        InsertNewCommnetLine(PostedWhseShptHeader, Format(WarehouseShipmentHeader."Cutt-off Date IPK"), WhseComment, LineNo, 'Cut Off: ');// Ambar sevk irs.
        //Not :
        InsertNewCommnetLine(PostedWhseShptHeader, Format(WarehouseShipmentHeader."Total Unit Quantity IPK"), WhseComment, LineNo, 'Ölçü Birimi: ');//Madde ölçü birimleri 


        HandleWarehouseLineCertifications(PostedWhseShptHeader, WarehouseShipmentHeader, WhseComment, LineNo);

    end;

    procedure AddCommentsToDomesticEInvoiceHeader(var EInvoiceHeader: Record "E-Invoice Header INF")
    var
        EInvoiceHeaderComment: Record "E-Invoice Header Comment INF";
        SalesInvoiceHeader: Record "Sales Invoice Header";
        LineNo: Integer;
    begin
        if not SalesInvoiceHeader.Get(EInvoiceHeader."No.") then
            exit;


        EInvoiceHeaderComment.SetRange("Document No.", EInvoiceHeader."No.");
        EInvoiceHeaderComment.SetRange("Document Type", EInvoiceHeader."Document Type");
        if EInvoiceHeaderComment.FindLast() then
            LineNo := EInvoiceHeaderComment."Comment Line No." + 10000
        else
            LineNo := 10000;

        if SalesInvoiceHeader."Ship-to Code" <> '' then begin
            InsertNewCommnetLineForEInvoiceHeader(EInvoiceHeader, Format(SalesInvoiceHeader."Ship-to Code"),/* EInvoiceHeaderComment,*/ LineNo, 'Bayi Kodu:  ');// Ship To Code
            InsertNewCommnetLineForEInvoiceHeader(EInvoiceHeader, Format(SalesInvoiceHeader."Ship-to Name"),/* EInvoiceHeaderComment,*/ LineNo, 'Bayi Adı:  ');//Ship To name
        end;
        InsertNewCommnetLineForEInvoiceHeader(EInvoiceHeader, Format(IpekBasicFunctions.GetUserFullNameFromSecurityId(UserSecurityId())),/* EInvoiceHeaderComment,*/ LineNo, '  ');//Ship To name



    end;

    var
        //Customer: Record Customer;
        //CustomerBankAccount: Record "Customer Bank Account";
        IpekPamukSetup: Record "Ipek Pamuk Setup IPK";
        GlobalLoadHeader: Record "Load Header IPK";
        //LoadHeader: Record "Load Header IPK";
        //WhseComment: Record "Warehouse Comment Line";
        ConfirmManagement: Codeunit "Confirm Management";
        IpekBasicFunctions: Codeunit "Ipek Basic Functions IPK";
        NewLot: Boolean;
        SkipDeletionOfLoad: Boolean;
        //LineNo: Integer;
        NoValidLineErr: Label 'There is no valid lines to ship.';
}