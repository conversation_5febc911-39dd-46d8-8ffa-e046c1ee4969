# İpek Pamuk Item Price Dataset - Comprehensive Documentation

## Executive Overview

### Business Purpose & Goals

The Item Price Dataset system is a sophisticated pricing analytics and cost management solution designed specifically for İpek Pamuk's textile manufacturing operations. This system provides comprehensive historical pricing data, cost tracking, and location-specific analytics to support strategic business decisions.

**Primary Business Objectives:**
- **Historical Price Analytics**: Maintain monthly granular pricing data from December 2024 onwards
- **Cost Management**: Track weighted average costs from purchase transactions and production activities  
- **Location-Specific Insights**: Analyze pricing variations across different warehouse locations
- **Production Planning Support**: Provide accurate costing data for production order items
- **Purchase Decision Support**: Analyze purchase price trends and cost variations
- **Multi-Currency Operations**: Support both Local Currency (LCY) and Additional Currency (ACY) reporting

### High-Level Architecture

The system implements a **dual-table architecture** with complementary pricing perspectives:

1. **Core Item Price Dataset** (`ItemPriceDatasetIPK` - Table 60030)
   - Company-wide item pricing without location specificity
   - Suitable for high-level strategic analysis
   - Optimized for production and purchase item categories

2. **Location-Specific Price Dataset** (`ItemPriceDatasetByLocIPK` - Table 60037)
   - Enhanced granularity with location-specific pricing
   - Supports multi-location cost analysis and inventory valuation
   - Essential for location-based decision making

### Integration with İpek Pamuk Operations

The Item Price Dataset system integrates seamlessly with İpek Pamuk's core business processes:

**Manufacturing Integration:**
- Production Order cost tracking and analysis
- Bill of Materials (BOM) cost calculation support
- Output entry cost capture and processing

**Inventory Management:**
- Location-specific inventory valuations
- End-of-month quantity tracking per location
- Variant-specific cost and price management

**Financial Reporting:**
- Dual currency support (Turkish Lira + EUR/USD)
- Weighted average cost calculations
- Manual price override capabilities for special scenarios

---

## Technical Architecture Documentation

### Core Object Structure

#### Tables

**1. Item Price Dataset IPK (Table 60030)**
```al
Primary Purpose: Company-wide item price and cost tracking
Key Features:
- Monthly granularity (MM.YYYY format)
- Auto-incrementing Entry No.
- Item and Variant support
- Dual currency fields (ACY/LCY)
- Manual price override capabilities
```

**Field Structure:**
- `Entry No.` (Integer, Auto-increment): Unique identifier
- `Item No.` (Code[20]): Item reference with validation
- `Variant Code` (Code[10]): Item variant support
- `Description` (Text[100]): Auto-populated from Item/Variant
- `Source Price Month` (Text[7]): MM.YYYY format (e.g., "01.2025")
- `Average Unit Price/Cost (ACY/LCY)` (Decimal): Calculated values
- `Manual Unit Price/Cost (ACY)` (Decimal): User override values
- `Quantity End of Month IPK` (Decimal): Inventory position
- `Start Date/End Date` (Date): Period boundaries
- `Use Manual Price` (Boolean): Override indicator

**2. Item Price Dataset By Loc IPK (Table 60037)**
```al
Primary Purpose: Location-specific price and cost tracking
Key Features:
- All features of core table plus Location dimension
- Location Code and Name fields
- Location-specific quantity calculations
- Enhanced granularity for multi-location analysis
```

**Additional Fields:**
- `Location Code` (Code[10]): Location reference
- `Location Name` (Text[100]): Auto-populated location description

#### Management Codeunits

**1. Item Price Dataset Mngt. 2 IPK (Codeunit 60014)**
- **Purpose**: Alternative processing approach using Item Ledger Entry combinations
- **Key Features**:
  - Discovers unique Item+Variant combinations from ledger entries
  - Single-pass processing with cost and sales calculations
  - Previous month data propagation
  - Testing capabilities for single items

**2. Item Price Dataset By Loc IPK (Codeunit 60032)**
- **Purpose**: Location-specific dataset processing
- **Key Features**:
  - Item+Location combination discovery
  - Location-aware cost and price calculations
  - Quantity calculations per location
  - Drill-down capabilities to source data

#### User Interface Pages

**1. Item Price Dataset List IPK (Page 60032)**
- **Purpose**: Main user interface for company-wide dataset
- **Key Features**:
  - Editable list view with drill-down capabilities
  - Processing actions for data generation
  - Value Entry navigation from calculated fields
  - Testing functions for development

**2. Item Price Dataset By Loc IPK (Page 60073)**
- **Purpose**: Location-specific dataset management
- **Key Features**:
  - Location dimension visibility
  - Location-specific processing actions
  - Enhanced drill-down with location filtering
  - Comprehensive data management functions

### Key Technical Features

#### Dual Currency Support
```al
Architecture:
- ACY (Additional Currency): Configurable foreign currency (EUR/USD)
- LCY (Local Currency): Turkish Lira (base currency)
- Currency Exchange Rate integration for sales calculations
- Manual override capabilities in ACY only
```

#### Monthly Granularity System
```al
Implementation:
- Source Price Month format: MM.YYYY
- December 2024 baseline establishment
- Progressive month creation up to current month
- Period boundaries (Start Date/End Date) for filtering
```

#### Manual Price Override System
```al
Business Logic:
- Manual Unit Price (ACY) and Manual Unit Cost (ACY) fields
- Use Manual Price boolean flag
- Processing logic respects manual overrides
- Manual price protection during batch processing
```

#### Performance Optimizations
```al
Techniques:
- Dictionary-based Item+Variant combination tracking
- Batch processing with progress reporting
- Optimized database queries with proper filtering
- Key-based record lookup strategies
```

---

## Business Logic Deep Dive

### Advanced Calculation Algorithms

#### Weighted Average Cost Calculation
```al
procedure CalculateCostForDatasetRecord(var DatasetRecord: Record "Item Price Dataset IPK")
{
    // 1. Filter Value Entries for specific Item+Variant+Period
    // 2. Include Purchase, Adjustments, and Output entries
    // 3. Exclude Rounding and Adjustment entries
    // 4. Calculate: Sum(Cost Amount) ÷ Sum(Valued Quantity)
    // 5. Store absolute values (handle credit memos)
    // 6. Fallback to previous month data if no current data
}
```

#### Weighted Average Sales Price Calculation
```al
procedure CalculateSalesForDatasetRecord(var DatasetRecord: Record "Item Price Dataset IPK")
{
    // 1. Filter Value Entries for Sales Invoice/Credit Memo
    // 2. Apply currency conversion for ACY calculations
    // 3. Calculate: Sum(Sales Amount) ÷ Sum(Invoiced Quantity)
    // 4. Store absolute values and currency code
    // 5. Propagate previous month data for gaps
}
```

#### Previous Month Data Propagation
```al
Logic:
- When no current month data exists, search for latest previous month record
- Copy Average Unit Price/Cost (ACY/LCY) and Currency Code
- Maintains pricing continuity across months
- Respects Item+Variant+Location combinations
```

#### Location-Specific Quantity Calculation
```al
procedure CalculateQuantityForDatasetRecord()
{
    // 1. Filter Item Ledger Entries by Item+Variant+Location
    // 2. Set date filter up to End Date of dataset record
    // 3. Use CalcSums(Quantity) for performance
    // 4. Store result in "Quantity End of Month IPK"
}
```

### Data Quality & Validation

#### Manual Price Protection Logic
```al
Business Rules:
- Check for non-zero Manual Unit Price (ACY) or Manual Unit Cost (ACY)
- Skip processing for Item+Variant+Month combinations with manual prices
- Force clear option available for administrative override
- Manual prices preserved during bulk operations
```

#### Data Integrity Checks
```al
Validations:
- Start Date cannot be later than End Date
- Item No. must exist in Item table
- Variant Code must exist for the item (if specified)
- Location Code must exist in Location table
- Currency Code validation against General Ledger Setup
```

---

## Integration Points Documentation

### Setup Dependencies

#### Ipek Pamuk Setup IPK Configuration
```al
Required Setup Fields:
- "Item Price Dataset Start Date": Defines processing start date
- Default value: 01.01.2025
- Used by Job Queue processing logic
- Configurable via Setup page (60007)
```

#### General Ledger Setup Integration
```al
ACY Currency Code Retrieval:
- Sources "Additional Reporting Currency" from GL Setup
- Supports EUR, USD, or other configured currencies
- Falls back to LCY if ACY not configured
- Used for all ACY amount calculations
```

### Standard Business Central Integration

#### Item Ledger Entry Dependency
```al
Integration Points:
- Item discovery from unique ledger entry combinations
- Quantity calculations using CalcSums on filtered entries
- Variant code auto-discovery from existing transactions
- Location code validation and combination detection
```

#### Value Entry Processing
```al
Source Data Extraction:
- Cost calculations from Purchase, Adjustment, Output value entries
- Sales price calculations from Sales value entries
- Currency amount handling (Cost Amount (Actual) vs (ACY))
- Document type filtering for invoice vs. credit memo processing
```

#### Currency Exchange Rate Integration
```al
Exchange Rate Processing:
- LCY to ACY conversion for sales amounts
- Date-specific exchange rate lookup
- Fallback to LCY if conversion fails
- Used in sales price calculations only
```

### Permission Management

#### Single Permission Set Approach
```al
Permission Set: "Ipek Pamuk Cust. IPK" (IpekPamukCustIPK.PermissionSet.al)
Granted Permissions:
- RIMD rights on Table 60030 "Item Price Dataset IPK"
- RIMD rights on Table 60037 "Item Price Dataset By Loc IPK"  
- Execute rights on all related codeunits and pages
- Integration with existing Ipek Pamuk permission structure
```

### Job Queue Framework Integration

#### Automated Processing (Codeunit 60031)
```al
Job Queue Entry Configuration:
- Table No.: Job Queue Entry
- OnRun Trigger: Validates setup and processes both item types
- Error Handling: Setup validation with meaningful error messages
- Processing Flow: 
  1. Validate "Item Price Dataset Start Date" configuration
  2. Process Production Order items using proven logic
  3. Process Purchase items using proven logic
  4. Log successful completion
```

#### Scheduling Recommendations
```al
Suggested Schedule:
- Frequency: Monthly (first working day of month)
- Timing: Off-peak hours (e.g., 2:00 AM)
- Prerequisites: Month-end closing procedures completed
- Dependencies: Item Ledger Entry and Value Entry posting finalized
```

---

## User Guide & Operational Procedures

### Data Processing Workflows

#### Standard Monthly Processing

**Step 1: Setup Verification**
1. Navigate to **Ipek Pamuk Setup IPK** (Page 60007)
2. Verify **"Item Price Dataset Start Date"** is configured
3. Confirm date aligns with your data processing requirements

**Step 2: Core Dataset Processing**
1. Open **Item Price Dataset List IPK** (Page 60032)
2. Execute **"Process All Items (New Method)"** action
3. Confirm processing dialog - this will:
   - Process all items with Item Ledger Entries
   - Create monthly records for December 2024 through current month
   - Calculate cost and sales prices from actual transactions
   - Display processing time and statistics

**Step 3: Location-Specific Processing (Optional)**
1. Open **Item Price Dataset By Loc IPK** (Page 60073)
2. Execute **"Process All Items"** action for comprehensive location analysis
3. Or execute **"Process Selected Location"** for specific location processing

**Step 4: Data Validation**
1. Review generated records for completeness
2. Use drill-down capabilities to verify calculation sources
3. Apply manual price overrides where necessary

#### Manual Price Management

**Setting Manual Prices:**
1. Locate the relevant dataset record (Item+Variant+Month combination)
2. Enter value in **"Manual Unit Price (ACY)"** or **"Manual Unit Cost (ACY)"**
3. System automatically sets **"Use Manual Price"** = true
4. Manual prices are protected during subsequent batch processing

**Removing Manual Prices:**
1. Clear the manual price field values (set to 0)
2. **"Use Manual Price"** flag will be reset
3. Next processing run will recalculate using actual transaction data

#### Location-Specific Analysis Procedures

**Viewing Location Performance:**
1. Open **Item Price Dataset By Loc IPK** page
2. Filter by **Location Code** for specific warehouse analysis
3. Use **"Source Price Month"** filter for period comparisons
4. Review **"Quantity End of Month IPK"** for inventory positions

**Comparing Location Costs:**
1. Filter by **Item No.** and **Source Price Month**
2. Sort by **"Average Unit Cost (ACY)"** to identify cost variations
3. Use drill-down to **Value Entries (Purchase)** to investigate differences

### Troubleshooting Guide

#### Common Data Issues

**Issue: No pricing data for certain items**
- **Cause**: Item has no Item Ledger Entries or Value Entries in the period
- **Solution**: Verify item transactions exist; check date filters
- **Prevention**: Ensure items have been transacted through invoices

**Issue: Pricing data seems incorrect**
- **Cause**: Manual prices may be overriding calculations
- **Solution**: Check **"Use Manual Price"** field and review manual values
- **Diagnosis**: Use drill-down to **Value Entries** to verify source data

**Issue: Location-specific data missing**
- **Cause**: Items may not have transactions at specific locations
- **Solution**: Process items individually for specific locations
- **Diagnosis**: Check Item Ledger Entries for location-specific transactions

#### Performance Issues

**Issue: Processing takes too long**
- **Cause**: Large volume of items or complex transaction history
- **Solution**: Process during off-peak hours; use single-item testing first
- **Optimization**: Consider processing by location or date range

**Issue: Memory or timeout errors**
- **Cause**: Excessive data volume in single processing run
- **Solution**: Clear existing data before processing; process in batches
- **Alternative**: Use **"Process Selected Location"** for smaller datasets

#### Drill-Down Navigation Techniques

**Cost Analysis Navigation:**
1. Click on **"Average Unit Cost (ACY)"** field in any record
2. System opens filtered **Value Entries** page showing:
   - Purchase Invoices and Credit Memos
   - Positive and Negative Adjustments
   - Output entries (for production items)
   - Filtered by Item, Variant, Location, and Date period

**Sales Price Analysis Navigation:**
1. Click on **"Average Unit Price (ACY)"** field in any record  
2. System opens filtered **Value Entries** page showing:
   - Sales Invoices and Credit Memos
   - Filtered by Item, Variant, Location, and Date period
   - Excludes rounding entries and adjustments

**Quantity Analysis Navigation:**
1. Click on **"Quantity End of Month IPK"** field
2. System opens filtered **Item Ledger Entries** page showing:
   - All transactions up to period end date
   - Filtered by Item, Variant, and Location (if applicable)
   - Provides complete audit trail for quantity calculations

---

## Development & Maintenance Guide

### Code Architecture Patterns

#### Management Codeunit Design Principles

**Separation of Concerns:**
```al
Pattern Implementation:
- Data Processing Logic: Separate procedures for each calculation type
- Error Handling: Centralized error messages and validation
- Performance Optimization: Dictionary-based combination tracking
- User Communication: Comprehensive progress and completion messages
```

**Processing Strategy Abstraction:**
```al
Two Distinct Approaches:
1. Ledger Entry-Based Processing (Codeunit 60014): Combination discovery
2. Location-Aware Processing (Codeunit 60032): Enhanced granularity
```

#### Performance Optimization Patterns

**Dictionary-Based Combination Tracking:**
```al
Implementation Example:
var
    ItemVariantCombinations: Dictionary of [Text, Boolean];
    CombinationKey: Text;

// Collection Phase
CombinationKey := ItemNo + '|' + VariantCode;
if not ItemVariantCombinations.ContainsKey(CombinationKey) then
    ItemVariantCombinations.Add(CombinationKey, true);

// Processing Phase
foreach CombinationKey in ItemVariantCombinations.Keys() do
    ProcessCombination(CombinationKey);
```

**Batch Processing with Progress Reporting:**
```al
Pattern:
1. Record processing start time
2. Initialize counters for different metrics
3. Process in logical batches (by item, location, etc.)
4. Calculate and display processing duration
5. Provide comprehensive completion statistics
```

#### Error Handling Patterns

**Validation-First Approach:**
```al
Implementation:
- Setup validation before processing begins
- Item existence verification during processing
- Date range validation with meaningful error messages
- Currency configuration validation
```

**Graceful Degradation:**
```al
Strategy:
- Continue processing other items if one item fails
- Provide detailed error context in messages
- Log successful operations despite partial failures
- Offer manual intervention points
```

### Extension Points

#### Adding New Calculation Methods

**To add new cost calculation logic:**
1. Create new procedure in appropriate management codeunit
2. Follow existing parameter patterns (`var DatasetRecord: Record`)
3. Implement source data filtering using established patterns
4. Add currency conversion logic for ACY calculations
5. Integrate with existing error handling framework

**Example Extension:**
```al
local procedure CalculateStandardCostForDatasetRecord(var DatasetRecord: Record "Item Price Dataset IPK")
var
    Item: Record Item;
begin
    if Item.Get(DatasetRecord."Item No.") then begin
        DatasetRecord."Average Unit Cost (ACY)" := ConvertToACY(Item."Standard Cost");
        DatasetRecord.Modify(true);
    end;
end;
```

#### Adding New Data Sources

**To integrate additional transaction types:**
1. Identify source table and relevant fields
2. Add filtering logic to existing calculation procedures
3. Ensure currency handling consistency
4. Update drill-down navigation in pages
5. Add comprehensive testing coverage

#### Extending Location Capabilities

**To add new location dimensions (e.g., Bin Code):**
1. Add field to `Item Price Dataset By Loc IPK` table
2. Update combination key generation logic
3. Enhance filtering in calculation procedures
4. Modify page layout to display new dimension
5. Update drill-down navigation accordingly

### Testing Procedures

#### Single-Item Testing Capability

**Available in Page 60032:**
```al
Action: "Process Single Item for Testing"
Purpose: Isolated item processing for development and troubleshooting
Features:
- Item selection dialog
- Complete processing cycle for single item
- Detailed timing and statistics
- Safe testing environment
```

**Testing Workflow:**
1. Use **"Process Single Item for Testing"** action
2. Select test item from Item List lookup
3. Review processing results and timing
4. Verify calculations using drill-down navigation
5. Clear test data before production processing

#### Data Validation Testing

**Recommended Test Cases:**
```al
Test Scenarios:
1. Item with no transactions (should handle gracefully)
2. Item with manual prices (should preserve overrides)
3. Item with multiple variants (should process all combinations)
4. Item with multiple locations (should create location-specific records)
5. Item with mixed currency transactions (should handle conversion)
```

#### Performance Testing

**Performance Benchmarks:**
```al
Acceptable Performance Targets:
- Single item processing: < 5 seconds
- 100 items processing: < 5 minutes  
- Full company processing: < 30 minutes
- Location-specific processing: < 15 minutes per location
```

### Maintenance Procedures

#### Regular Maintenance Tasks

**Monthly:**
- Review processing performance metrics
- Validate manual price overrides for business relevance
- Check for orphaned records (items no longer in use)
- Verify currency exchange rate accuracy

**Quarterly:**
- Analyze data growth and storage requirements
- Review and optimize slow-performing queries
- Update documentation for any process changes
- Validate integration points remain functional

**Annually:**
- Archive historical data beyond business requirements
- Review and update object permissions as needed
- Assess performance optimization opportunities
- Plan for system capacity growth

#### Data Archival Strategy

**Recommended Approach:**
```al
Archival Criteria:
- Data older than 3 years (configurable)
- Items no longer active in system
- Locations no longer in use
- Zero-value records with no business relevance
```

#### Backup and Recovery

**Critical Data Elements:**
- Manual price overrides (high business value)
- Setup configuration (system continuity)
- Processing history and timing metrics (troubleshooting)

**Recovery Procedures:**
- Full system recovery: Rebuild from Item Ledger Entries
- Partial recovery: Reprocess specific time periods
- Configuration recovery: Restore setup values from backup

---

## Conclusion

The İpek Pamuk Item Price Dataset system represents a sophisticated, production-ready solution for textile manufacturing cost management and pricing analytics. Its dual-table architecture, comprehensive calculation algorithms, and extensive integration capabilities provide a robust foundation for strategic business decision-making.

**Key Strengths:**
- **Comprehensive Coverage**: Handles both production and purchase item categories
- **Location Granularity**: Supports multi-warehouse cost analysis
- **Manual Override System**: Balances automation with user control
- **Performance Optimization**: Efficient processing of large datasets
- **Audit Transparency**: Complete drill-down capabilities to source data

**Recommended Usage Patterns:**
- **Monthly Processing**: Automated job queue processing for regular updates
- **Strategic Analysis**: Location-specific cost comparisons and trends
- **Production Planning**: Historical cost data for accurate budgeting
- **Exception Management**: Manual price overrides for special scenarios

This documentation provides the foundation for effective system utilization, ongoing maintenance, and future enhancement initiatives.