page 60076 "ILE - Tracing IPK"
{
    ApplicationArea = All;
    Caption = 'ILE - Tracing';
    PageType = List;
    SourceTable = "Item Ledger Entry";
    UsageCategory = Lists;
    SourceTableTemporary = true;
    Editable = true;


    layout
    {
        area(Content)
        {
            group(LotNoToTrace)
            {
                Caption = 'Lot No. To Trace';
                field(LotNoToTrace1; LotNoToTrace)
                {
                    Caption = 'Lot No. To Trace';
                    ToolTip = 'Specifies the lot number you want to trace through the supply chain.';

                    trigger OnValidate()
                    begin
                        if LotNoToTrace <> '' then
                            LotNoToTrace := UpperCase(LotNoToTrace);
                    end;
                }
                field(TraceResultsSummary; TraceResultsSummary)
                {
                    Caption = 'Trace Results Summary';
                    Editable = false;
                    MultiLine = true;
                    ToolTip = 'Specifies a summary of the trace results.';
                }
            }

            repeater(General)
            {
                field("Entry No."; Rec."Entry No.")
                {
                    ToolTip = 'Specifies the number of the entry, as assigned from the specified number series when the entry was created.';
                }
                field("Posting Date"; Rec."Posting Date")
                {
                    ToolTip = 'Specifies the entry''s posting date.';
                }
                field("Document No."; Rec."Document No.")
                {
                    ToolTip = 'Specifies the document number on the entry. The document is the voucher that the entry was based on, for example, a receipt.';

                    trigger OnDrillDown()
                    begin
                        OpenRelatedDocument();
                    end;
                }
                field("Entry Type"; Rec."Entry Type")
                {
                    ToolTip = 'Specifies which type of transaction that the entry is created from.';
                    Style = Strong;
                    StyleExpr = (Rec."Entry Type" = Rec."Entry Type"::Purchase) or (Rec."Entry Type" = Rec."Entry Type"::Sale);
                }
                field("Source No."; Rec."Source No.")
                {
                    ToolTip = 'Specifies where the entry originated.';
                }
                field("Item No."; Rec."Item No.")
                {
                    ToolTip = 'Specifies the number of the item in the entry.';
                }
                field("Variant Code"; Rec."Variant Code")
                {
                    ToolTip = 'Specifies the variant of the item on the line.';
                }
                field(Description; Rec.Description)
                {
                    ToolTip = 'Specifies a description of the entry.';
                }
                field("Location Code"; Rec."Location Code")
                {
                    ToolTip = 'Specifies the location where the item entry was registered.';
                }
                field("Lot No."; Rec."Lot No.")
                {
                    ToolTip = 'Specifies a lot number if the posted item carries such a number.';
                }
                field("Source Lot No. IPK"; Rec."Source Lot No. IPK")
                {
                }
                field(Quantity; Rec.Quantity)
                {
                    ToolTip = 'Specifies the number of units of the item in the item entry.';
                }
                field("Remaining Quantity"; Rec."Remaining Quantity")
                {
                    ToolTip = 'Specifies the remaining quantity in the entry.';
                }
                field("Indent IPK"; Rec."Indent IPK")
                {
                }
            }
        }
    }

    actions
    {
        area(Processing)
        {
            action(TraceLotNo)
            {
                ApplicationArea = All;
                Caption = 'Trace Lot Number';
                Image = ItemTracking;
                Promoted = true;
                PromotedCategory = Process;
                PromotedIsBig = true;
                PromotedOnly = true;
                ToolTip = 'Traces the entered lot number through the entire supply chain.';

                trigger OnAction()
                begin
                    if LotNoToTrace = '' then
                        Error(ErrEnterLotNoToTraceLbl);

                    // Clear previous results
                    Rec.Reset();
                    Rec.DeleteAll(false);
                    TraceResultsSummary := '';

                    LotTracingManagement.TraceLot(LotNoToTrace, Rec);
                    CurrPage.Update(false);
                end;
            }
        }
    }

    var
        LotTracingManagement: Codeunit "Lot Tracing Management IPK";
        LotNoToTrace: Text;
        TraceResultsSummary: Text;
        ErrEnterLotNoToTraceLbl: Label 'Please enter a Lot Number to trace.';

    local procedure OpenRelatedDocument()
    var
        SalesShipmentHeader: Record "Sales Shipment Header";
        PurchaseReceiptHeader: Record "Purch. Rcpt. Header";
        TransferShipmentHeader: Record "Transfer Shipment Header";
        TransferReceiptHeader: Record "Transfer Receipt Header";
        ProductionOrder: Record "Production Order";
        WarehouseReceiptHeader: Record "Warehouse Receipt Header";
        PostedWhseReceiptHeader: Record "Posted Whse. Receipt Header";
        PageManagement: Codeunit "Page Management";
        DocumentFound: Boolean;
    begin
        if Rec."Document No." = '' then
            exit;

        DocumentFound := false;

        case Rec."Entry Type" of
            Rec."Entry Type"::Purchase:
                begin
                    // Try Purchase Receipt first
                    if PurchaseReceiptHeader.Get(Rec."Document No.") then begin
                        PageManagement.PageRun(PurchaseReceiptHeader);
                        DocumentFound := true;
                    end;

                    // If not found, try Warehouse Receipt
                    if not DocumentFound then
                        if WarehouseReceiptHeader.Get(Rec."Document No.") then begin
                            PageManagement.PageRun(WarehouseReceiptHeader);
                            DocumentFound := true;
                        end;

                    // If not found, try Posted Warehouse Receipt
                    if not DocumentFound then
                        PostedWhseReceiptHeader.SetRange("Whse. Receipt No.", Rec."Document No.");
                    if PostedWhseReceiptHeader.FindFirst() then begin
                        PageManagement.PageRun(PostedWhseReceiptHeader);
                        DocumentFound := true;
                    end;
                end;

            Rec."Entry Type"::Sale:
                // Try Sales Shipment
                if SalesShipmentHeader.Get(Rec."Document No.") then begin
                    PageManagement.PageRun(SalesShipmentHeader);
                    DocumentFound := true;
                end;

            Rec."Entry Type"::Transfer:
                begin
                    // Try Transfer Shipment first
                    if TransferShipmentHeader.Get(Rec."Document No.") then begin
                        PageManagement.PageRun(TransferShipmentHeader);
                        DocumentFound := true;
                    end;

                    // If not found, try Transfer Receipt
                    if not DocumentFound then
                        if TransferReceiptHeader.Get(Rec."Document No.") then begin
                            PageManagement.PageRun(TransferReceiptHeader);
                            DocumentFound := true;
                        end;
                end;

            Rec."Entry Type"::Output, Rec."Entry Type"::Consumption:
                begin
                    // Try Production Order
                    ProductionOrder.SetRange("No.", Rec."Document No.");
                    if ProductionOrder.FindFirst() then begin
                        PageManagement.PageRun(ProductionOrder);
                        DocumentFound := true;
                    end;
                end;
        end;

        if not DocumentFound then
            Message('Document %1 could not be found.', Rec."Document No.");
    end;
}