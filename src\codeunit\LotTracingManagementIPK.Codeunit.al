codeunit 60077 "Lot Tracing Management IPK"
{
    Access = Internal;
    procedure TraceLot(LotNo: Text; var TempItemledger: Record "Item Ledger Entry" temporary)
    var
        ItemLedgerEntry: Record "Item Ledger Entry";
        LotNoFilter: Text;
        Indent: Integer;
    begin
        ItemLedgerEntry.SetFilter("Entry Type", '%1|%2|%3|%4', ItemLedgerEntry."Entry Type"::Purchase, ItemLedgerEntry."Entry Type"::Consumption, ItemLedgerEntry."Entry Type"::Sale, ItemLedgerEntry."Entry Type"::Output);

        repeat
            Indent += 1;

            ItemLedgerEntry.SetFilter("Lot No.", LotNo);
            if ItemLedgerEntry.FindSet(false) then
                repeat
                    TempItemledger.Init();
                    TempItemledger.TransferFields(ItemLedgerEntry);
                    TempItemledger."Indent IPK" := Indent;
                    if not TempItemledger.Insert(false) then
                        continue;

                    if ItemLedgerEntry."Source Lot No. IPK" <> '' then
                        if StrPos(LotNoFilter, ItemLedgerEntry."Source Lot No. IPK") = 0 then
                            LotNoFilter := LotNoFilter + ItemLedgerEntry."Source Lot No. IPK" + '|';

                until ItemLedgerEntry.Next() = 0;

            if LotNoFilter <> '' then
                LotNoFilter := DelStr(LotNoFilter, StrLen(LotNoFilter), 1);

            LotNo := LotNoFilter;
            LotNoFilter := '';

        until LotNo = '';
    end;
}
