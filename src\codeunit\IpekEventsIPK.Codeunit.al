codeunit 60001 "Ipek Events IPK"

{
    Access = Internal;
    SingleInstance = true;
    Permissions = tabledata "Sales Invoice Header" = RIMD;
    #region Production Events

    // [EventSubscriber(ObjectType::Codeunit, Codeunit::"Prod. Order Status Management", OnBeforeOnRun, '', false, false)]
    // local procedure "Prod. Order Status Management_OnBeforeOnRun"(var ChangeStatusOnProdOrderPage: Page "Change Status on Prod. Order"; var ProductionOrder: Record "Production Order"; var IsHandled: Boolean; NewStatus: Enum "Production Order Status"; NewPostingDate: Date; NewUpdateUnitCost: Boolean)
    // begin
    //     if ProductionOrder."Last Production Date-Time IPK" <> 0DT then
    //         NewPostingDate := ProductionOrder."Last Production Date-Time IPK".Date;
    //     NewUpdateUnitCost := true;
    //     // ChangeStatusOnProdOrderPage.
    // end;

    [EventSubscriber(ObjectType::Page, Page::"Change Status on Prod. Order", OnAfterSet, '', false, false)]
    local procedure "Change Status on Prod. Order_OnAfterSet"(var Sender: Page "Change Status on Prod. Order"; ProdOrder: Record "Production Order"; var PostingDate: Date; var ReqUpdUnitCost: Boolean)
    begin
        if ProdOrder."Last Production Date-Time IPK" <> 0DT then
            PostingDate := ProdOrder."Last Production Date-Time IPK".Date();
        ReqUpdUnitCost := true;
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Create Prod. Order Lines", OnCopyFromFamilyOnBeforeInsertProdOrderLine, '', false, false)]
    local procedure Create_Prod_Order_Lines_OnCopyFromFamilyOnBeforeInsertProdOrderLine(var ProdOrderLine: Record "Prod. Order Line"; FamilyLine: Record "Family Line")
    begin
        ProdOrderLine.Validate("Variant Code", FamilyLine."Variant Code IPK");
    end;

    [EventSubscriber(ObjectType::Table, Database::"Production Order", OnBeforeAssignItemNo, '', false, false)]
    local procedure "Production Order_OnBeforeAssignItemNo"(var ProdOrder: Record "Production Order"; xProdOrder: Record "Production Order"; var Item: Record Item; CallingFieldNo: Integer)
    begin
        ProdOrder.TestField("Location Code");
    end;

    [EventSubscriber(ObjectType::Table, Database::"Production Order", OnBeforeAssignFamily, '', false, false)]
    local procedure "Production Order_OnBeforeAssignFamily"(var ProdOrder: Record "Production Order"; xProdOrder: Record "Production Order"; var Family: Record Family; CallingFieldNo: Integer)
    begin
        ProdOrder.TestField("Location Code");
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Create Prod. Order from Sale", OnAfterCreateProdOrderFromSalesLine, '', false, false)]
    local procedure "Create Prod. Order from Sale_OnAfterCreateProdOrderFromSalesLine"(var ProdOrder: Record "Production Order"; var SalesLine: Record "Sales Line")
    begin
        IpekProductionManagement.CreateProdOrderfromSale_OnAfterCreateProdOrderFromSalesLine(ProdOrder, SalesLine);
        ProdOrder."Location Code" := 'UHD';//Setupa eklenecek


    end;

    [EventSubscriber(ObjectType::Report, Report::"Calc. Consumption", OnCreateConsumpJnlLineOnAfterAssignItemTracking, '', false, false)]
    local procedure "Calc. Consumption_OnCreateConsumpJnlLineOnAfterAssignItemTracking"(var ItemJnlLine: Record "Item Journal Line"; var NextConsumpJnlLineNo: Integer)
    begin
        IpekProductionManagement.AssignItemTrackingInformationFromItemJournalConsumptionLine(ItemJnlLine, false);

    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Item Jnl.-Post", OnBeforeCode, '', false, false)]
    local procedure "Item Jnl.-Post_OnBeforeCode"(var ItemJournalLine: Record "Item Journal Line"; var HideDialog: Boolean; var SuppressCommit: Boolean; var IsHandled: Boolean)
    begin
        HideDialog := true;
        SuppressCommit := true;
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Calculate Prod. Order", OnAfterTransferBOMComponent, '', false, false)]
    local procedure "Calculate Prod. Order_OnAfterTransferBOMComponent"(var ProdOrderLine: Record "Prod. Order Line"; var ProductionBOMLine: Record "Production BOM Line"; var ProdOrderComponent: Record "Prod. Order Component"; LineQtyPerUOM: Decimal; ItemQtyPerUOM: Decimal)
    begin
        IpekProductionManagement.SetLocationToProdOrderComponent(ProdOrderLine, ProdOrderComponent);
    end;

    // [EventSubscriber(ObjectType::Table, Database::"Warehouse Shipment Header", OnBeforeGetLocation, '', false, false)]
    // local procedure "Warehouse Shipment Header_OnBeforeGetLocation"(LocationCode: Code[10]; var WarehouseShipmentHeader: Record "Warehouse Shipment Header"; var Location: Record Location; var IsHandled: Boolean)
    // var
    //     Customer: Record Customer;
    // begin
    //     Customer.loc
    //     IsHandled := true;
    // end;

    #endregion Production Events

    #region Sales Events

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Sales Warehouse Mgt.", OnBeforeCreateShptLineFromSalesLine, '', false, false)]
    local procedure Sales_Warehouse_Mgt_OnBeforeCreateShptLineFromSalesLine(var WarehouseShipmentLine: Record "Warehouse Shipment Line"; WarehouseShipmentHeader: Record "Warehouse Shipment Header"; SalesLine: Record "Sales Line"; SalesHeader: Record "Sales Header")
    begin
        IpekSalesManagement.UpdateQuantities(WarehouseShipmentLine, SalesLine);
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Get Source Doc. Inbound", OnAfterCreateWhseReceiptHeaderFromWhseRequest, '', false, false)]
    local procedure "Get Source Doc. Inbound_OnAfterCreateWhseReceiptHeaderFromWhseRequest"(var WhseReceiptHeader: Record "Warehouse Receipt Header"; var WarehouseRequest: Record "Warehouse Request"; var GetSourceDocuments: Report "Get Source Documents")
    var
        PurchaseHeader: Record "Purchase Header";
        WarehouseReceiptLine: Record "Warehouse Receipt Line";
        ConfirmManagement: Codeunit "Confirm Management";
    begin
        IpekPamukSetup.GetRecordOnce();
        WarehouseReceiptLine.SetRange("No.", WhseReceiptHeader."No.");
        WarehouseReceiptLine.DeleteQtyToReceive(WarehouseReceiptLine);
        WarehouseReceiptLine.Reset();
        WarehouseReceiptLine.SetRange("No.", WhseReceiptHeader."No.");
        WarehouseReceiptLine.FindFirst();
        PurchaseHeader.Get(PurchaseHeader."Document Type"::Order, WarehouseReceiptLine."Source No.");
        WhseReceiptHeader.Validate("Vendor No. IPK", PurchaseHeader."Pay-to Vendor No.");
        WhseReceiptHeader.Modify(true);
        if ConfirmManagement.GetResponse('Do you want to send a mail to quality control users?') then
            OnAfterCreateWarehouseReciptBe(IpekBasicFunctions.GetUserNameFromSecurityId(WhseReceiptHeader.SystemModifiedBy), WhseReceiptHeader."No.", WarehouseReceiptLine."Source No.", PurchaseHeader."Pay-to Name",
                                             IpekPamukSetup."Warehouse Recipt Created M.G.");
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Get Source Doc. Inbound", OnAfterGetSingleInboundDoc, '', false, false)]
    local procedure "Get Source Doc. Inbound_OnAfterGetSingleInboundDoc"(var WarehouseReceiptHeader: Record "Warehouse Receipt Header")
    var
        PurchaseHeader: Record "Purchase Header";
        WarehouseReceiptLine: Record "Warehouse Receipt Line";
        ConfirmManagement: Codeunit "Confirm Management";
    begin
        IpekPamukSetup.GetRecordOnce();
        WarehouseReceiptLine.Reset();
        WarehouseReceiptLine.SetRange("No.", WarehouseReceiptHeader."No.");
        WarehouseReceiptLine.FindFirst();
        PurchaseHeader.Get(PurchaseHeader."Document Type"::Order, WarehouseReceiptLine."Source No.");
        WarehouseReceiptHeader.Validate("Vendor No. IPK", PurchaseHeader."Pay-to Vendor No.");
        WarehouseReceiptHeader.Modify(true);
        if ConfirmManagement.GetResponse('Do you want to send a mail to quality control users?') then
            OnAfterCreateWarehouseReciptBe(IpekBasicFunctions.GetUserNameFromSecurityId(WarehouseReceiptHeader.SystemModifiedBy), WarehouseReceiptHeader."No.", WarehouseReceiptLine."Source No.", PurchaseHeader."Pay-to Name",
                                             IpekPamukSetup."Warehouse Recipt Created M.G.");

    end;

    [EventSubscriber(ObjectType::Report, Report::"Get Source Documents", OnAfterSalesLineOnPreDataItem, '', false, false)]
    local procedure "Get Source Documents_OnAfterSalesLineOnPreDataItem"(var Sender: Report "Get Source Documents"; var SalesLine: Record "Sales Line"; OneHeaderCreated: Boolean; WhseShptHeader: Record "Warehouse Shipment Header"; WhseReceiptHeader: Record "Warehouse Receipt Header")
    begin
        SalesLine.SetFilter("Warehouse Qty. to Ship IPK", '>%1', 0);
    end;

    [EventSubscriber(ObjectType::Table, Database::"Sales Shipment Line", OnBeforeInsertInvLineFromShptLine, '', false, false)]
    local procedure "Sales Shipment Line_OnBeforeInsertInvLineFromShptLine"(var SalesShptLine: Record "Sales Shipment Line"; var SalesLine: Record "Sales Line"; SalesOrderLine: Record "Sales Line"; var IsHandled: Boolean; var TransferOldExtTextLines: Codeunit "Transfer Old Ext. Text Lines")
    begin
        SalesLine."FOB Piece Unit Price IPK" := SalesShptLine."FOB Piece Unit Price IPK";
        SalesLine."Freight Inc PieceUnitPrice IPK" := SalesShptLine."Freight Inc PieceUnitPrice IPK";
        SalesLine."FOB Line Amount IPK" := SalesShptLine."FOB Line Amount IPK";
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Whse.-Post Shipment", OnBeforePostedWhseShptHeaderInsert, '', false, false)]
    local procedure "Whse.-Post Shipment_OnBeforePostedWhseShptHeaderInsert"(var PostedWhseShipmentHeader: Record "Posted Whse. Shipment Header"; WarehouseShipmentHeader: Record "Warehouse Shipment Header")
    begin
        IpekPamukSetup.GetRecordOnce();
        IpekSalesManagement.AssignValuesToPostedWarehouseShipmentHeader(PostedWhseShipmentHeader, WarehouseShipmentHeader);
        IpekSalesManagement.WhsePostShipment_OnBeforePostedWhseShptHeaderInsertFunction(PostedWhseShipmentHeader);
        OnAfterShipLoadCardBe(IpekBasicFunctions.GetUserNameFromSecurityId(WarehouseShipmentHeader.SystemModifiedBy), WarehouseShipmentHeader."Source No. INF", WarehouseShipmentHeader."Sell-to Customer Name INF", WarehouseShipmentHeader."No.", PostedWhseShipmentHeader."No.", IpekPamukSetup."Load Card Mail Group");
    end;

    // [EventSubscriber(ObjectType::Codeunit, Codeunit::"Whse.-Post Shipment", OnAfterPostedWhseShptHeaderInsert, '', false, false)]
    // local procedure "Whse.-Post Shipment_OnAfterPostedWhseShptHeaderInsert"(PostedWhseShipmentHeader: Record "Posted Whse. Shipment Header"; LastShptNo: Code[20])
    // begin
    //     if PostedWhseShipmentHeader."Single Shipment Per Order IPK" then
    //         IpekSalesManagement.HandleSingleShipment(PostedWhseShipmentHeader);
    // end;




    [EventSubscriber(ObjectType::Table, Database::"Warehouse Shipment Line", OnBeforeUpdateDocumentStatus, '', false, false)]
    local procedure "Warehouse Shipment Line_OnBeforeUpdateDocumentStatus"(var WarehouseShipmentLine: Record "Warehouse Shipment Line"; var IsHandled: Boolean)

    begin
        IpekSalesManagement.WarehouseShipmentLine_OnBeforeUpdateDocumentStatus(WarehouseShipmentLine);
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Whse.-Post Shipment", OnBeforeDeleteUpdateWhseShptLine, '', false, false)]
    local procedure "Whse.-Post Shipment_OnBeforeDeleteUpdateWhseShptLine"(WhseShptLine: Record "Warehouse Shipment Line"; var DeleteWhseShptLine: Boolean; var WhseShptLineBuf: Record "Warehouse Shipment Line")
    begin
        IpekSalesManagement.WhsePostShipment_OnBeforeDeleteUpdateWhseShptLine();
    end;

    // [EventSubscriber(ObjectType::Codeunit, Codeunit::"Whse.-Post Shipment", OnBeforePostedWhseShptHeaderInsert, '', false, false)]
    // local procedure "Whse.-Post Shipment_OnBeforePostedWhseShptHeaderInsert"(var PostedWhseShptHeader: Record "Posted Whse. Shipment Header"; var WarehouseShipmentHeader: Record "Warehouse Shipment Header")
    // begin

    // end;
    [EventSubscriber(ObjectType::Table, Database::"Sales Line", OnBeforeValidateUnitPrice, '', false, false)]
    local procedure "Sales Line_OnBeforeValidateUnitPrice"(var SalesLine: Record "Sales Line"; CurrentFieldNo: Integer; var IsHandled: Boolean)
    begin
        if SalesLine."Units per Parcel" = 0 then
            exit;

        // SalesLine."FOB Piece Unit Price IPK" := SalesLine."Unit Price" / SalesLine."Units per Parcel";
        SalesLine."Freight Inc PieceUnitPrice IPK" := SalesLine."Unit Price" / SalesLine."Units per Parcel";//"Q/O Unit Price INF"
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Create E-Shipment NAV Doc. INF", OnAfterCreateEShipmentLineFromWhShipment, '', false, false)]
    local procedure "Create E-Shipment NAV Doc. INF_OnAfterCreateEShipmentLineFromWhShipment"(PostedWhseShipmentHeader: Record "Posted Whse. Shipment Header"; PostedWhseShipmentLine: Record "Posted Whse. Shipment Line"; var EShipmentLine: Record "E-Shipment Line INF")
    begin
        EShipmentLine.Validate("Buyers Item Identification", PostedWhseShipmentLine."Item Reference No. IPK");
        EShipmentLine.Modify(true);
    end;

    // [EventSubscriber(ObjectType::Codeunit, Codeunit::"Sales-Post", OnAfterSalesInvHeaderInsert, '', false, false)]
    // local procedure "Sales-Post_OnAfterSalesInvHeaderInsert"(var SalesInvHeader: Record "Sales Invoice Header"; SalesHeader: Record "Sales Header"; CommitIsSuppressed: Boolean; WhseShip: Boolean; WhseReceive: Boolean; var TempWhseShptHeader: Record "Warehouse Shipment Header"; var TempWhseRcptHeader: Record "Warehouse Receipt Header"; PreviewMode: Boolean)
    // begin
    //     SalesInvHeader.Validate("Export No. IPK", SalesHeader."Export No. IPK");
    //     SalesInvHeader.Modify(true);
    // end;
    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Sales-Post", OnAfterInsertInvoiceHeader, '', false, false)]
    local procedure "Sales-Post_OnAfterInsertInvoiceHeader"(var SalesHeader: Record "Sales Header"; var SalesInvHeader: Record "Sales Invoice Header")
    begin
        SalesInvHeader.Validate("Export No. IPK", SalesHeader."Export No. IPK");

        SalesInvHeader.Modify(true);
    end;

    // [EventSubscriber(ObjectType::Codeunit, Codeunit::"Sales-Post", OnBeforeSalesInvHeaderInsert, '', false, false)]
    // local procedure "Sales-Post_OnBeforeSalesInvHeaderInsert"(var SalesInvHeader: Record "Sales Invoice Header"; var SalesHeader: Record "Sales Header"; CommitIsSuppressed: Boolean; var IsHandled: Boolean; WhseShip: Boolean; WhseShptHeader: Record "Warehouse Shipment Header"; InvtPickPutaway: Boolean)
    // begin
    //     SalesInvHeader."Export No. IPK" := SalesHeader."Export No. IPK";
    //     SalesInvHeader.Modify(true);
    // end;
    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Warehouse Ship Events INF", OnAfterTransferFieldsFromSalesHeaderToWhShipHdr, '', false, false)]
    local procedure "Warehouse Ship Events INF_OnAfterTransferFieldsFromSalesHeaderToWhShipHdr"(SalesHeader: Record "Sales Header"; var Rec: Record "Warehouse Shipment Header")
    begin
        Rec.Validate("Sell-to Source No. INF");
    end;


    #endregion Sales Events

    #region Purchase Events


    // [EventSubscriber(ObjectType::Codeunit, Codeunit::"Purch.-Post", OnAfterPurchCrMemoHeaderInsert, '', false, false)]
    // local procedure "Purch.-Post_OnAfterPurchCrMemoHeaderInsert"(var PurchCrMemoHdr: Record "Purch. Cr. Memo Hdr."; var PurchHeader: Record "Purchase Header"; CommitIsSupressed: Boolean; PreviewMode: Boolean)
    // begin

    // end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Purch.-Post", OnBeforePurchCrMemoHeaderInsert, '', false, false)]
    local procedure "Purch.-Post_OnBeforePurchCrMemoHeaderInsert"(var PurchCrMemoHdr: Record "Purch. Cr. Memo Hdr."; var PurchHeader: Record "Purchase Header"; CommitIsSupressed: Boolean)
    begin
        if PurchHeader."Vendor Invoice No." <> '' then begin
            PurchHeader."Vendor Cr. Memo No." := PurchHeader."Vendor Invoice No.";
            PurchCrMemoHdr."Vendor Cr. Memo No." := PurchHeader."Vendor Invoice No.";
        end;
    end;

    // [EventSubscriber(ObjectType::Table, Database::"Vendor Ledger Entry", OnAfterInsertEvent, '', false, false)]
    // local procedure "Purch.-Post_OnAfterInsertEvent"(var Rec: Record "Vendor Ledger Entry"; RunTrigger: Boolean)
    // begin
    //     // PurchCrMemoHdr."Vendor Cr. Memo No." := PurchHeader."Vendor Invoice No.";
    //     // PurchCrMemoHdr."Pre-Assigned No." := PurchHeader."Vendor Invoice No.";
    // end;

    // [EventSubscriber(ObjectType::Table, Database::"Vendor Ledger Entry", OnBeforeInsertEvent, '', false, false)]
    // local procedure "Purch.-Post_OnBeforeInsertEvent"(var Rec: Record "Vendor Ledger Entry"; RunTrigger: Boolean)
    // begin
    //     // PurchCrMemoHdr."Vendor Cr. Memo No." := PurchHeader."Vendor Invoice No.";
    //     // PurchCrMemoHdr."Pre-Assigned No." := PurchHeader."Vendor Invoice No.";
    //     Message('alper');
    // end;

    // [EventSubscriber(ObjectType::Codeunit, Codeunit::"Purch.-Post", OnInsertICGenJnlLineOnAfterCopyDocumentFields, '', false, false)]
    // local procedure "Purch.-Post_OnInsertICGenJnlLineOnAfterCopyDocumentFields"(PurchaseHeader: Record "Purchase Header"; PurchaseLine: Record "Purchase Line"; var TempICGenJournalLine: Record "Gen. Journal Line")
    // begin
    //     Message(('alşper'));
    // end;

    [EventSubscriber(ObjectType::Table, Database::"Gen. Journal Line", OnAfterCopyGenJnlLineFromPurchHeader, '', false, false)]
    local procedure "Gen. Journal Line_OnAfterCopyGenJnlLineFromPurchHeader"(PurchaseHeader: Record "Purchase Header"; var GenJournalLine: Record "Gen. Journal Line")
    begin
        if (PurchaseHeader."Document Type" = PurchaseHeader."Document Type"::"Credit Memo") and (PurchaseHeader."Vendor Invoice No." <> '') then begin
            PurchaseHeader."Vendor Cr. Memo No." := PurchaseHeader."Vendor Invoice No.";
            PurchaseHeader.Modify(true);
        end;
    end;


    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Whse.-Post Receipt", OnAfterWhseRcptLineSetFilters, '', false, false)]
    local procedure "Whse.-Post Receipt_OnAfterWhseRcptLineSetFilters"(var WarehouseReceiptLine: Record "Warehouse Receipt Line")
    var
        ConfirmManagement: Codeunit "Confirm Management";
        ProcessAbortedErr: Label 'Process Aborted';
        WareRecptQcErr: Label 'All warehouse receipt lines must be checked for quality control before posting.';
    begin
        if WarehouseReceiptLine.Quantity <> WarehouseReceiptLine."Qty. to Receive" then
            if not ConfirmManagement.GetResponseOrDefault('"Quantity" different then "Quantity To Recive" Do you want to continue ?') then
                Error(ProcessAbortedErr);
        WarehouseReceiptLine.SetRange("Quality Control Doc. Stat. IPK", Enum::"Quality Control Status QCM"::"Input Pending");
        if not WarehouseReceiptLine.IsEmpty() then
            Error(WareRecptQcErr);

        WarehouseReceiptLine.SetRange("Quality Control Doc. Stat. IPK");
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Get Source Doc. Inbound", OnAfterSetWarehouseRequestFilters, '', false, false)]
    local procedure "Get Source Doc. Inbound_OnAfterSetWarehouseRequestFilters"(var WarehouseRequest: Record "Warehouse Request"; WarehouseReceiptHeader: Record "Warehouse Receipt Header")
    begin
        if WarehouseReceiptHeader."Vendor No. IPK" <> '' then
            WarehouseRequest.SetRange("Destination No.", WarehouseReceiptHeader."Vendor No. IPK");
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Whse.-Create Source Document", OnBeforeWhseReceiptLineInsert, '', false, false)]
    local procedure "Whse.-Create Source Document_OnBeforeWhseReceiptLineInsert"(var WarehouseReceiptLine: Record "Warehouse Receipt Line")
    var
        Item: Record Item;
    begin
        if not Item.Get(WarehouseReceiptLine."Item No.") then
            exit;

        WarehouseReceiptLine."Lot No. IPK" := NoSeries.GetNextNo(Item."Lot Nos.");
        IpekQualityManagement.GenerateQualityControlDocumentFromWareHauseRecptLine(WarehouseReceiptLine);
    end;
    //will be tested
    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Whse.-Post Receipt", OnBeforeCode, '', false, false)]
    local procedure "Whse.-Post Receipt_OnBeforeCode"(WhseRcptLine: Record "Warehouse Receipt Line"; var SuppressCommit: Boolean; CounterSourceDocOK: Integer; CounterSourceDocTotal: Integer; var IsHandled: Boolean)
    begin
        SuppressCommit := true;
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Whse.-Post Receipt", OnAfterCheckWhseRcptLine, '', false, false)]
    local procedure "Whse.-Post Receipt_OnAfterCheckWhseRcptLine"(var WarehouseReceiptLine: Record "Warehouse Receipt Line")
    begin

        IpekQualityManagement.AutoTransferAfterWarehouseReciptPost(WarehouseReceiptLine);
    end;
    // [EventSubscriber(ObjectType::Codeunit, Codeunit::"Whse.-Post Receipt", OnCodeOnAfterRemoveSourceFilterFromWhseRcptLine, '', false, false)]
    // local procedure "Whse.-Post Receipt_OnCodeOnAfterRemoveSourceFilterFromWhseRcptLine"(var WarehouseReceiptLine: Record "Warehouse Receipt Line"; var PurchaseHeader: Record "Purchase Header"; var SalesHeader: Record "Sales Header"; var TransferHeader: Record "Transfer Header")
    // begin
    // end;

    // [EventSubscriber(ObjectType::Codeunit, Codeunit::"Whse.-Post Receipt", OnCodeOnAfterPostSourceDocuments, '', false, false)]
    // local procedure "Whse.-Post Receipt_OnAfterCheckWhseRcptLine"(var WarehouseReceiptHeader: Record "Warehouse Receipt Header";var WarehouseReceiptLine: Record "Warehouse Receipt Line")
    // begin
    //     IpekQualityManagement.AutoTransferAfterWarehouseReciptPost(WarehouseReceiptLine);
    // end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Whse.-Post Receipt", OnAfterCode, '', false, false)]
    local procedure "Whse.-Post Receipt_OnAfterCode"(var WarehouseReceiptHeader: Record "Warehouse Receipt Header"; WarehouseReceiptLine: Record "Warehouse Receipt Line"; CounterSourceDocTotal: Integer; CounterSourceDocOK: Integer)
    begin

        // IpekQualityManagement.PostAutoTransferOrders(WarehouseReceiptHeader);
        IpekPamukSetup.GetRecordOnce();
        WarehouseReceiptHeader.CalcFields("Vendor Name IPK");
        OnAfterShipWarehouseReciptBe(IpekBasicFunctions.GetUserNameFromSecurityId(WarehouseReceiptHeader.SystemModifiedBy), WarehouseReceiptHeader."No.", WarehouseReceiptLine."Source No.", WarehouseReceiptHeader."Vendor Name IPK",
                                        WarehouseReceiptLine."Item No.", WarehouseReceiptLine."Qty. Received", IpekPamukSetup."Warehouse Recipt Mail Group");
    end;



    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Purchases Warehouse Mgt.", OnPurchLine2ReceiptLineOnAfterSetQtysOnRcptLine, '', false, false)]
    local procedure "Purchases Warehouse Mgt._OnPurchLine2ReceiptLineOnAfterSetQtysOnRcptLine"(var WarehouseReceiptLine: Record "Warehouse Receipt Line"; PurchaseLine: Record "Purchase Line")
    begin
        WarehouseReceiptLine.Quantity := PurchaseLine."Whse. Rcpt. Qty-to Receive IPK";
        WarehouseReceiptLine."Qty. (Base)" := PurchaseLine."Whse. Rcpt. Qty-to Receive IPK";
        WarehouseReceiptLine.Validate("Qty. Received", 0);
        WarehouseReceiptLine.Validate("Qty. Outstanding", PurchaseLine."Whse. Rcpt. Qty-to Receive IPK");
        WarehouseReceiptLine.Validate("Qty. to Receive", 0);

        PurchaseLine."Whse. Rcpt. Qty-to Receive IPK" := 0;
        PurchaseLine.Modify(true);
    end;

    [EventSubscriber(ObjectType::Report, Report::"Get Source Documents", OnAfterPurchaseLineOnPreDataItem, '', false, false)]
    local procedure "Get Source Documents_OnAfterPurchaseLineOnPreDataItem"(var Sender: Report "Get Source Documents"; var PurchaseLine: Record "Purchase Line"; OneHeaderCreated: Boolean; WhseShptHeader: Record "Warehouse Shipment Header"; WhseReceiptHeader: Record "Warehouse Receipt Header")
    begin
        PurchaseLine.SetFilter("Whse. Rcpt. Qty-to Receive IPK", '>0');
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Whse.-Post Shipment", OnAfterCreatePostedShptHeader, '', false, false)]
    local procedure "Whse.-Post Shipment_OnAfterCreatePostedShptHeader"(var PostedWhseShptHeader: Record "Posted Whse. Shipment Header"; var WarehouseShipmentHeader: Record "Warehouse Shipment Header")
    begin
        IpekSalesManagement.AddCommentsToExportEShipmentHeader(PostedWhseShptHeader, WarehouseShipmentHeader);
        // IpekSalesManagement.AddCommentsToPostedWarehouseDocument(PostedWhseShptHeader, WarehouseShipmentHeader);

    end;


    // [EventSubscriber(ObjectType::Page, Page::"Warehouse Receipt", OnAfterActionEvent, "Post Receipt", false, false)]
    // local procedure OnAfterActionEvent_WarehouseReceipt_PostReceipt(var Rec: Record "Warehouse Receipt Header")
    // begin

    //     IpekQualityManagement.PostAutoTransferOrders(Rec);
    // end;
    #endregion Purchase Events
    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Create E-Shipment NAV Doc. INF", OnBeforeCreateEShipmentLineFromWhShipmentLine, '', false, false)]
    local procedure "Create E-Shipment NAV Doc. INF_OnBeforeCreateEShipmentLineFromWhShipmentLine"(var PostedWhseShipmentLine: Record "Posted Whse. Shipment Line"; var EShipmentHeader: Record "E-Shipment Header INF"; var ItemLineSequenceID: Integer; var IsHandled: Boolean)
    var
        EShipmentLineComment: Record "E-Shipment Line Comment INF";
        Item: Record Item;
    begin
        Item.Get(PostedWhseShipmentLine."Item No.");
        if Item."Units per Parcel" <> 0 then begin
            // EShipmentLine.Get(EShipmentHeader."Document Type", EShipmentHeader."No.", PostedWhseShipmentLine."Line No.");
            EShipmentLineComment.Init();
            EShipmentLineComment."Document Type" := EShipmentHeader."Document Type";
            EShipmentLineComment."Document No." := EShipmentHeader."No.";
            EShipmentLineComment."Line No." := PostedWhseShipmentLine."Line No.";
            EShipmentLineComment.Insert(true);
            EShipmentLineComment."Comment Text" := Format(Item."Units per Parcel" * PostedWhseShipmentLine.Quantity);
            EShipmentLineComment.Modify(true);
        end;
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Create E-Shipment NAV Doc. INF", OnBeforeCloseEShipmentNAVDocumentCreation, '', false, false)]
    local procedure "Create E-Shipment NAV Doc. INF_OnBeforeCloseEShipmentNAVDocumentCreation"(var EShipmentHeader: Record "E-Shipment Header INF"; var ShipmentDocument: Variant)
    var
        EShipmentLine: Record "E-Shipment Line INF";
        Item: Record Item;
        ItemNo: Code[20];
    begin
        EShipmentLine.SetRange("Document No.", EShipmentHeader."No.");
        EShipmentLine.SetRange("Document Type", EShipmentHeader."Document Type");
        EShipmentLine.FindSet();
        repeat
            ItemNo := CopyStr(EShipmentLine."Sellers Item Identification", 1, MaxStrLen(ItemNo));
            if Item.Get(ItemNo) then begin
                EShipmentLine.Name := Item.Description;
                EShipmentLine.Modify(true);
            end;
        until EShipmentLine.Next() = 0;

        //OnAfterCreatedEShipmentNavDocument(EShipmentHeader."Document Type", EShipmentHeader."No.", EShipmentHeader.SystemId);
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Item Jnl.-Post Line", OnAfterInsertItemLedgEntry, '', false, false)]
    local procedure "Item Jnl.-Post Line_OnAfterInsertItemLedgEntry"(var ItemLedgerEntry: Record "Item Ledger Entry"; ItemJournalLine: Record "Item Journal Line"; var ItemLedgEntryNo: Integer; var ValueEntryNo: Integer; var ItemApplnEntryNo: Integer; GlobalValueEntry: Record "Value Entry"; TransferItem: Boolean; var InventoryPostingToGL: Codeunit "Inventory Posting To G/L"; var OldItemLedgerEntry: Record "Item Ledger Entry")
    begin
        IpekPamukSetup.GetRecordOnce();

        case ItemLedgerEntry."Entry Type" of
            ItemLedgerEntry."Entry Type"::"Negative Adjmt.":
                OnAfterInsertItemLedgEntryBe(ItemLedgerEntry."Item No.", ItemLedgerEntry.Quantity, 'Negatif', IpekPamukSetup."Item Ledger Mail Group");
            ItemLedgerEntry."Entry Type"::"Positive Adjmt.":
                OnAfterInsertItemLedgEntryBe(ItemLedgerEntry."Item No.", ItemLedgerEntry.Quantity, 'Pozitif', IpekPamukSetup."Item Ledger Mail Group");

        end;

    end;

    [ExternalBusinessEvent('OnAfterInsertItemLedgEntry', 'On After Insert Item Ledg Entry', 'Triggered whenOn After Insert Item Ledg Entry', EventCategory::"Infotek Events")]
    procedure OnAfterInsertItemLedgEntryBe(ItemNo: Code[20]; ItemQuantity: Decimal; LedgerType: Text[100]; SendTo: Text[200])
    var
    begin
    end;

    [ExternalBusinessEvent('OnAfterShipLoadCard', 'On After Ship Load Card', 'Triggered when After Ship Load Card', EventCategory::"Infotek Events")]
    procedure OnAfterShipLoadCardBe(UserName: Code[50]; SalesOrderNo: Code[20]; CustomerName: Text[100]; ShipmentNo: Code[20]; PostedDocumentNo: Code[20]; SendTo: Text[200])
    var
    begin
    end;

    [ExternalBusinessEvent('OnAfterShipWarehouseReciptBe', 'On After Ship Warehouse Recipt Be', 'Triggered when On After Ship Warehouse Recipt', EventCategory::"Infotek Events")]
    procedure OnAfterShipWarehouseReciptBe(UserName: Code[50]; WarehouseReciptNo: Code[20]; PurchaseOrderNo: Code[20]; VendorName: Text[100]; FirstItemNo: Code[20]; FirstItemQtyToRecive: Decimal; SendTo: Text[200])
    var
    begin
    end;

    [ExternalBusinessEvent('OnAfterCreateWarehouseReciptBe', 'On After Create Warehouse Recipt Be', 'Triggered when On After Create Warehouse Recipt', EventCategory::"Infotek Events")]
    procedure OnAfterCreateWarehouseReciptBe(UserName: Code[50]; WarehouseReciptNo: Code[20]; PurchaseOrderNo: Code[20]; VendorName: Text[100]; SendTo: Text[200])
    var
    begin
    end;

    [ExternalBusinessEvent('QualityControlStatusChanged', 'Quality Control Status Changed', 'Triggered when Quality Control Status Changed', EventCategory::"Infotek Events")]
    procedure QualityControlStatusChangedBe(UserName: Code[50]; ItemNo: Code[20]; LotNo: Code[50]; PurchaseOrderNo: Code[20]; QualityControlNo: Code[20]; SendTo: Text[250]; Status: Enum "Quality Control Status QCM")
    var
    begin
    end;

    [ExternalBusinessEvent('ProductionBOMApprovalRequest', 'Production BOM Approval Request', 'Triggered when Production BOM Approval Request', EventCategory::"Infotek Events")]
    procedure ProductionBOMApprovalRequest(ItemNo: Code[20]; UserName: Code[50]; Url: Text[250]; MailList: Text[250])
    var
    begin
    end;



    local procedure AddCommentToEInvoiceLine(EInvLine: Record "E-Inv. Ln. Goods&Services INF"; CommentText: Text[250])
    var
        EInvoiceLineComment: Record "E-Invoice Line Comment INF";
    begin
        EInvoiceLineComment.Init();
        EInvoiceLineComment."Document Type" := EInvLine."Document Type";
        EInvoiceLineComment."Document No." := EInvLine."Document No.";
        EInvoiceLineComment."Line No." := EInvLine."Line No.";
        EInvoiceLineComment.Insert(true);
        EInvoiceLineComment."Comment Text" := CommentText;
        EInvoiceLineComment.Modify(true);
    end;

    local procedure HandleEExportLineQuantities(var SalesLine: Record "Sales Line"; var EInvLine: Record "E-Inv. Ln. Goods&Services INF")
    var
        EInvoiceHeader: Record "E-Invoice Header INF";
        EInvoiceSetup: Record "E-Invoice Setup INF";
        Item: Record Item;
        ItemTranslation: Record "Item Translation";
    begin
        EInvoiceSetup.FindFirst();
        EInvoiceHeader.SetRange("No.", EInvLine."Document No.");
        EInvoiceHeader.FindFirst();
        Item.Get(SalesLine."No.");

        ItemTranslation.SetRange("Item No.", Item."No.");
        if SalesLine."Variant Code" <> '' then
            ItemTranslation.SetRange("Variant Code", SalesLine."Variant Code");
        ItemTranslation.FindFirst();

        if EInvoiceHeader."Profile ID" = EInvoiceSetup."E-Invoice Profile for Export" then
            if (Item."Units per Parcel" <> 0) and (Item."Sales Unit of Measure" = 'KOLI') then begin
                EInvLine.Quantity := Item."Units per Parcel" * EInvLine.Quantity;
                // EInvLine."Unit Price" := SalesLine."Unit Price" / Item."Units per Parcel";

                EInvLine."UoM Code (International Std.)" := 'C62';
                EInvLine."UoM Code (NAV)" := 'ADET';

            end;


        EInvLine."Unit Price" := SalesLine."Freight Inc PieceUnitPrice IPK";
        // EInvLine.Description := Item."No." + ' * ' + ItemTranslation.Description;
        EInvLine.Name := ItemTranslation.Description;
        EInvLine.Modify(true);
    end;

    local procedure CreateEInvLinePackageDetailOnInsert(var SalesLine: Record "Sales Line"; var EInvLine: Record "E-Inv. Ln. Goods&Services INF")
    var
        EInvLinePackageDetail: Record "E-Inv. Line Package Detail INF";
    begin
        EInvLinePackageDetail.Init();
        EInvLinePackageDetail."Document Type" := EInvLine."Document Type";
        EInvLinePackageDetail."Document No." := EInvLine."Document No.";
        EInvLinePackageDetail."Line No." := EInvLine."Line No.";
        EInvLinePackageDetail."Detail Line No." := 10000;
        EInvLinePackageDetail.Insert(true);
        EInvLinePackageDetail."Actual Package ID" := '1';
        EInvLinePackageDetail.Quantity := SalesLine.Quantity;
        EInvLinePackageDetail."Packging Type Code" := 'CT';
        EInvLinePackageDetail.Modify(true);
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Create E-Shipment NAV Doc. INF", OnAfterMakeNecessaryCorrectionsForExportShipment, '', false, false)]
    local procedure "Create E-Shipment NAV Doc. INF_OnAfterMakeNecessaryCorrectionsForExportShipment"(var EShipmentHeader: Record "E-Shipment Header INF"; var ShipHeader: Variant)
    var
        EShipmentSetup: Record "E-Shipment Setup INF";
    begin
        EShipmentSetup.Get();
        EShipmentHeader."Delivery Cust. Name" := EShipmentSetup."Delivery Cust. Name IPK";
        EShipmentHeader."Delivery Cust. Street Name" := EShipmentSetup."Delivery Cust. Street Name IPK";
        EShipmentHeader."Delivery Cust. City Subdiv." := EShipmentSetup."Del. Cust. City Subdiv. IPK";
        EShipmentHeader."Delivery Cust. City Name" := EShipmentSetup."Delivery Cust. City Name IPK";
        EShipmentHeader."Delivery Cust. Postal Zone" := EShipmentSetup."Delivery Cust. Postal Zone IPK";
        EShipmentHeader."Delivery Cust. Country Code" := EShipmentSetup."Del. Cust. Country Code IPK";
        EShipmentHeader."Delivery Cust. Country" := EShipmentSetup."Delivery Cust. Country IPK";
        EShipmentHeader."Delivery Cust. Tax Schema Name" := EShipmentSetup."Del. Cust. Tax Schema Name IPK";
        EShipmentHeader."Customer Mailbox Label" := CopyStr(EShipmentSetup."Ministry of Cust.Mailbox Label", 1, MaxStrLen(EShipmentHeader."Customer Mailbox Label"));
        EShipmentHeader.Modify(true);

    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Create E-Shipment NAV Doc. INF", OnAfterCreateEShipmentLinesFromWhShipment, '', false, false)]
    local procedure "Create E-Shipment NAV Doc. INF_OnAfterCreateEShipmentLinesFromWhShipment"(PostedWhseShipmentHeader: Record "Posted Whse. Shipment Header"; var EShipmentHeader: Record "E-Shipment Header INF")
    begin
        IpekSalesManagement.MakeNecessaryEditionsToDomesticEShipment(PostedWhseShipmentHeader, EShipmentHeader);
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Create NAV EInv SlsUnpstd INF", OnAfterCreateEInvoiceLineFromCurrentSalesLine, '', false, false)]
    local procedure "Create NAV EInv SlsUnpstd INF_OnAfterCreateEInvoiceLineFromCurrentSalesLine"(var SalesHeader: Record "Sales Header"; var SalesLine: Record "Sales Line"; var EInvLine: Record "E-Inv. Ln. Goods&Services INF")
    begin
        HandleEExportLineQuantities(SalesLine, EInvLine);

        CreateEInvLinePackageDetailOnInsert(SalesLine, EInvLine);
        //EInvLine. := SalesHeader.city şehir, ship tp state posta kodu ulke bolge
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Create NAV E-Inv Sales INF", OnAfterCreateEInvoiceLineFromCurrentSalesInvoiceLine, '', false, false)]
    local procedure "Create NAV E-Inv Sales INF_OnAfterCreateEInvoiceLineFromCurrentSalesInvoiceLine"(var SalesInvoiceHeader: Record "Sales Invoice Header"; var SalesInvoiceLine: Record "Sales Invoice Line"; var EInvLine: Record "E-Inv. Ln. Goods&Services INF")
    var
        EInvoiceSetup: Record "E-Invoice Setup INF";
        Item: Record Item;
        CommentText: Text[250];
    begin
        EInvoiceSetup.Get();
        if SalesInvoiceHeader."E-Invoice Profile ID INF" <> EInvoiceSetup."E-Invoice Profile for Export" then begin
            if SalesInvoiceLine.Type = SalesInvoiceLine.Type::Item then
                Item.Get(SalesInvoiceLine."No.");
            CommentText := Format('Ölçü Birimi:' + Format(Item."Units per Parcel" * EInvLine.Quantity));
            AddCommentToEInvoiceLine(EInvLine, CommentText);

        end;

    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"E-Invoice Management INF", OnAfterSetEInvoiceHeaderDefaultStatus, '', false, false)]
    local procedure "E-Invoice Management INF_OnAfterSetEInvoiceHeaderDefaultStatus"(var EInvoiceHeader: Record "E-Invoice Header INF"; EInvoiceSetup: Record "E-Invoice Setup INF")
    begin

        IpekSalesManagement.AddCommentsToDomesticEInvoiceHeader(EInvoiceHeader);
        IpekSalesManagement.AddCommentsToEExport(EInvoiceHeader);
        IpekSalesManagement.AddCommentsToEInvoice(EInvoiceHeader);
        // EditEInvoiceBeforeCreate(EInvoiceHeader);

    end;

    // procedure EditEInvoiceBeforeCreate(var EInvoiceHeader: Record "E-Invoice Header INF")
    // var
    //     SalesInvoiceHeader: Record "Sales Invoice Header";
    // begin
    //     SalesInvoiceHeader.SetRange("No.", EInvoiceHeader."No.");
    //     if SalesInvoiceHeader.FindFirst() then begin
    //         if (SalesInvoiceHeader."Currency Code" <> '') and (SalesInvoiceHeader."Currency Factor" > 0) then
    //             EInvoiceHeader."TL Amount IPK" := SalesInvoiceHeader."Amount Including VAT" * (1 / SalesInvoiceHeader."Currency Factor")
    //         else
    //             EInvoiceHeader."TL Amount IPK" := SalesInvoiceHeader."Amount Including VAT";

    //         // EInvoiceHeader."Export No. IPK" := SalesInvoiceHeader."Export No. IPK";
    //         EInvoiceHeader.Modify(true);
    //     end;
    // end;


    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Purch.-Post", OnBeforeCheckExternalDocumentNumber, '', false, false)]
    local procedure "Purch.-Post_OnBeforeCheckExternalDocumentNumber"(VendorLedgerEntry: Record "Vendor Ledger Entry"; PurchaseHeader: Record "Purchase Header"; var Handled: Boolean; DocType: Option; ExtDocNo: Text[35]; SrcCode: Code[10]; GenJnlLineDocType: Enum "Gen. Journal Document Type"; GenJnlLineDocNo: Code[20]; var GenJnlPostLine: Codeunit "Gen. Jnl.-Post Line"; var TotalPurchLine: Record "Purchase Line"; var TotalPurchLineLCY: Record "Purchase Line")
    var
        Vendor: Record Vendor;
        VendorLedgerEntryTemp: Record "Vendor Ledger Entry";
        InvoiceDuplicationNoticeMsg: Label 'This external document no has already been refunded';
    begin
        Vendor.Get(PurchaseHeader."Buy-from Vendor No.");
        if Vendor."Skip Ext. Doc.No. Unq. IPK" then begin
            Handled := true;
            VendorLedgerEntryTemp.SetRange("Document Type", VendorLedgerEntryTemp."Document Type"::"Credit Memo");
            VendorLedgerEntryTemp.SetRange("External Document No.", ExtDocNo);
            if not VendorLedgerEntryTemp.IsEmpty() then
                Error(InvoiceDuplicationNoticeMsg);
        end;
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Sales-Post", OnBeforePostSalesDoc, '', false, false)]
    local procedure "Sales-Post_OnBeforePostSalesDoc"(var Sender: Codeunit "Sales-Post"; var SalesHeader: Record "Sales Header"; CommitIsSuppressed: Boolean; PreviewMode: Boolean; var HideProgressWindow: Boolean; var IsHandled: Boolean; var CalledBy: Integer)
    var
        CustLedgerEntry: Record "Cust. Ledger Entry";
        InvoiceDuplicationNoticeMsg: Label 'This external document no has already been refunded';
    begin
        if SalesHeader."Document Type" = SalesHeader."Document Type"::"Credit Memo" then begin
            CustLedgerEntry.SetRange("Customer No.", SalesHeader."Bill-to Customer No.");
            CustLedgerEntry.SetRange("Document Type", CustLedgerEntry."Document Type"::"Credit Memo");
            CustLedgerEntry.SetRange("External Document No.", SalesHeader."External Document No.");
            if not CustLedgerEntry.IsEmpty() then
                Error(InvoiceDuplicationNoticeMsg);
        end;
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Gen. Jnl.-Post Line", OnBeforeCheckPurchExtDocNo, '', false, false)]
    local procedure "Gen. Jnl.-Post Line_OnBeforeCheckPurchExtDocNo"(GenJournalLine: Record "Gen. Journal Line"; VendorLedgerEntry: Record "Vendor Ledger Entry"; CVLedgerEntryBuffer: Record "CV Ledger Entry Buffer"; var Handled: Boolean)
    var
        Vendor: Record Vendor;
    begin
        Vendor.Get(VendorLedgerEntry."Vendor No.");
        if Vendor."Skip Ext. Doc.No. Unq. IPK" then
            Handled := true;

    end;


    // [EventSubscriber(ObjectType::Codeunit, Codeunit::"Whse.-Create Source Document", OnBeforeWhseShptLineInsert, '', false, false)]
    // local procedure "Whse.-Create Source Document_OnBeforeWhseShptLineInsert"(var WarehouseShipmentLine: Record "Warehouse Shipment Line")
    // var
    //     WarehouseShipmentLine2: Record "Warehouse Shipment Line";
    // begin
    //     WarehouseShipmentLine2.SetRange("No.", WarehouseShipmentLine."No.");
    //     WarehouseShipmentLine2.SetFilter("Line No.", '<>%1', WarehouseShipmentLine."Line No.");
    //     WarehouseShipmentLine2.SetRange("Item No.", WarehouseShipmentLine."Item No.");
    //     WarehouseShipmentLine2.SetRange("Variant Code", WarehouseShipmentLine."Variant Code");
    //     if WarehouseShipmentLine2.FindFirst() then
    //         Message(Format(WarehouseShipmentLine2."Line No."));

    // end;

    // [EventSubscriber(ObjectType::Codeunit, Codeunit::"Whse.-Create Source Document", OnAfterWhseShptLineInsert, '', false, false)]
    // local procedure "Whse.-Create Source Document_OnAfterWhseShptLineInsert"(var WarehouseShipmentLine: Record "Warehouse Shipment Line")
    // begin
    //     Message('a');
    // end;

    // [EventSubscriber(ObjectType::Codeunit, Codeunit::"Whse.-Create Source Document", OnAfterCreateShptLine, '', false, false)]
    // local procedure "Whse.-Create Source Document_OnAfterCreateShptLine"(var WarehouseShipmentLine: Record "Warehouse Shipment Line")
    // begin
    //     Message('b');
    // end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Sales-Get Shipment", OnAfterCreateInvLines, '', false, false)]
    local procedure "Sales-Get Shipment_OnAfterCreateInvLines"(var Sender: Codeunit "Sales-Get Shipment"; var SalesShipmentLine2: Record "Sales Shipment Line"; var SalesHeader: Record "Sales Header"; var SalesLine: Record "Sales Line"; SalesShipmentHeader: Record "Sales Shipment Header")
    var
        SalesHeaderOrder: Record "Sales Header";
    begin
        SalesHeaderOrder.SetRange("No.", SalesShipmentHeader."Order No.");
        SalesHeaderOrder.FindFirst();

        SalesHeader."Shipping Agent Code" := SalesHeaderOrder."Shipping Agent Code";
        SalesHeader."Shipping Agent Service Code" := SalesHeaderOrder."Shipping Agent Service Code";
        SalesHeader."Transaction Specification" := SalesHeaderOrder."Transaction Specification";
        SalesHeader."Exit Point" := SalesHeaderOrder."Exit Point";
        // SalesHeader."Transport Means INF" := SalesHeaderOrder."Transport Means INF";

        SalesHeader."Transport Method" := SalesHeaderOrder."Transport Method";
        SalesHeader.Modify(true);
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Item Jnl.-Post Line", OnBeforeInsertItemLedgEntry, '', false, false)]
    local procedure "Item Jnl.-Post Line_OnBeforeInsertItemLedgEntry"(var ItemLedgerEntry: Record "Item Ledger Entry"; ItemJournalLine: Record "Item Journal Line"; TransferItem: Boolean; OldItemLedgEntry: Record "Item Ledger Entry"; ItemJournalLineOrigin: Record "Item Journal Line")
    begin
        IpekProductionManagement.SetItemLedgerEntrySourceLotNo(ItemLedgerEntry);
    end;


    var
        IpekPamukSetup: Record "Ipek Pamuk Setup IPK";
        IpekBasicFunctions: Codeunit "Ipek Basic Functions IPK";
        IpekProductionManagement: Codeunit "Ipek Production Management IPK";
        IpekQualityManagement: Codeunit "Ipek Quality Management IPK";
        IpekSalesManagement: Codeunit "Ipek Sales Management IPK";
        NoSeries: Codeunit "No. Series";

}