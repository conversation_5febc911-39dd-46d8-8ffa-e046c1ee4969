page 60012 "Package Creation IPK"
{
    ApplicationArea = All;
    Caption = 'Package Creation IPK';
    PageType = StandardDialog;
    SourceTable = "Package Creation IPK";
    SourceTableTemporary = true;

    layout
    {
        area(Content)
        {
            field("Source Type"; Rec."Source Type")
            {
                ToolTip = 'Specifies the value of the Source Type field.';
            }
            field("Document No."; Rec."Document No.")
            {
                Editable = DocumentNoEditable;
                ToolTip = 'Specifies the value of the Document No. field.';
            }
            field("Document Line No."; Rec."Document Line No.")
            {
                Editable = DocumentLineNoEditable;
                ToolTip = 'Specifies the value of the Document Line No. field.';
                Visible = false;
            }
            field("Item No."; Rec."Item No.")
            {
                Editable = false;
                ToolTip = 'Specifies the value of the Item No. field.';
            }
            field("Variant Code"; Rec."Variant Code")
            {
                Editable = false;
            }
            field("Item Decription"; Rec."Item Description")
            {
                Editable = false;
                ToolTip = 'Specifies the value of the Item Description field.';
            }
            field("Lot No."; Rec."Lot No.")
            {
                Editable = false;
                ToolTip = 'Specifies the value of the Lot No. field.';
            }
            field("Production Center"; Rec."Produced By")
            {
                Editable = not DocumentIsPurchase;
                Visible = not DocumentIsPurchase;
            }
            field("Max Available Quantity"; Rec."Max Available Quantity")
            {
                Caption = 'Max Available Quantity';
                Editable = false;
            }
            field("Creation Method"; Rec."Creation Method")
            {
                ToolTip = 'Specifies the value of the Creation Method field.';
                // Editable = not DocumentNoEditable;
                ValuesAllowed = Single, Multiple;
            }
            field("Package Count"; Rec."Package Count")
            {
                Editable = Rec."Creation Method" = Rec."Creation Method"::Multiple;
                ToolTip = 'Specifies the value of the Package Count field.';
                trigger OnValidate()
                var
                    TooMuchPackCountErr: Label 'You cannot enter more then 100 package at once';
                begin
                    if Rec."Package Count" > 50 then
                        Error(TooMuchPackCountErr);
                    if DocumentIsPurchase then
                        CalcTotalPackageQuantity(Rec."Package Count", Rec."Package Quantity")
                    else
                        CalcSinglePackageQuantity(Rec."Package Count", TotalPackageQuantity);

                    if TotalPackageQuantity > Rec."Max Available Quantity" then
                        Error(CannotGreaterErr, TotalPackageQuantity, Rec."Max Available Quantity");


                end;
            }
            field("Package Quantity"; Rec."Package Quantity")
            {
                Editable = DocumentIsPurchase;
                ToolTip = 'Specifies the value of the Package Quantity field.';
                trigger OnValidate()
                var
                    PalletItemMsg: Label 'The number of items per pallet is %1. You are trying to place %2 items on this pallet.', Comment = '%1="Package Creation IPK"."Item Pieces Of Pallet"; %2="Package Creation IPK"."Package Quantity"';
                begin
                    if DocumentIsPurchase then begin
                        CalcTotalPackageQuantity(Rec."Package Count", Rec."Package Quantity");

                        if (TotalPackageQuantity > Rec."Max Available Quantity") then
                            Error(CannotGreaterErr, TotalPackageQuantity, Rec."Max Available Quantity");

                        if Rec."Source Type" = Rec."Source Type"::Production then
                            if Rec."Package Quantity" > Rec."Item Pieces Of Pallet" then
                                Message(PalletItemMsg, Rec."Item Pieces Of Pallet", Rec."Package Quantity");
                    end
                end;
            }
            field("Total Package Quantity"; TotalPackageQuantity)
            {
                Caption = 'Total Package Quantity';
                ToolTip = 'Specifies the total quantity of the packages.';
                Editable = not DocumentIsPurchase;
                trigger OnValidate()
                var
                    PalletItemMsg: Label 'The number of items per pallet is %1. You are trying to place %2 items on this pallet.', Comment = '%1="Package Creation IPK"."Item Pieces Of Pallet"; %2="Package Creation IPK"."Package Quantity"';

                begin
                    if not DocumentIsPurchase then begin
                        CalcSinglePackageQuantity(Rec."Package Count", TotalPackageQuantity);

                        if (TotalPackageQuantity > Rec."Max Available Quantity") then
                            Error(CannotGreaterErr, TotalPackageQuantity, Rec."Max Available Quantity");

                        if Rec."Source Type" = Rec."Source Type"::Production then
                            if Rec."Package Quantity" > Rec."Item Pieces Of Pallet" then
                                Message(PalletItemMsg, Rec."Item Pieces Of Pallet", Rec."Package Quantity");
                    end
                end;
            }
            field("Order Quantity"; Rec."Order Quantity")
            {
                Editable = false;
            }
        }
    }
    trigger OnQueryClosePage(CloseAction: Action): Boolean
    var
        PackageNoInformation: Record "Package No. Information";
        //TempPackageNoInformation: Record "Package No. Information" temporary;
        ProductionOrder: Record "Production Order";
        i: Integer;
        NoPackageCreatedMsg: Label 'No package was created.';
        FilterString: Text;
    begin
        if not (CloseAction = Action::OK) then
            Message(NoPackageCreatedMsg)
        else
            case Rec."Source Type" of
                "Package Creation Source Type"::Purchase:
                    IpekPurchaseManagement.CreateWarehouseReceiptLineDetailsFromPackageCreation(Rec);
                "Package Creation Source Type"::Production:
                    begin
                        i := 0;
                        while i < Rec."Package Count" do begin
                            if FilterString = '' then
                                FilterString := FilterString + IpekProductionManagement.CreateProductionOrderLineDetailsFromPackageCreation(Rec)
                            else
                                FilterString := FilterString + '|' + IpekProductionManagement.CreateProductionOrderLineDetailsFromPackageCreation(Rec);
                            i += 1;

                        end;

                        PackageNoInformation.SetFilter("Package No.", FilterString);
                        Report.Run(Report::"Palette Label IPK", true, true, PackageNoInformation);

                        if ProductionOrder.Get(ProductionOrder.Status::Released, Rec."Document No.") then
                            IpekProductionManagement.UpdateLastProducedDateTime(ProductionOrder, CurrentDateTime());
                    end;
            // PackageNoInformationTemp.FindSet();
            // testint := PackageNoInformationTemp.Count();

            end;



        // PackageNoInformationTemp.FindSet();


    end;

    procedure CalcTotalPackageQuantity(PackageCount: Integer; PackageQuantity: Decimal)
    begin
        TotalPackageQuantity := (PackageCount * PackageQuantity);
        // Rec."Total Package Quantity" := TotalPackageQuantity;
    end;

    procedure CalcSinglePackageQuantity(PackageCount: Integer; ParTotalPackageQuantity: Decimal)
    begin
        if (PackageCount > 0) and (ParTotalPackageQuantity > 0) then
            Rec."Package Quantity" := (ParTotalPackageQuantity / PackageCount);
        // Rec."Total Package Quantity" := TotalPackageQuantity;
    end;

    trigger OnAfterGetRecord()
    var
        WarehouseReceiptLine: Record "Warehouse Receipt Line";
    begin
        // case üretimden geldiğinde gereksiz alanların visible'ı kapatılacak

        if Rec."Source Type"::Purchase = Rec."Source Type" then begin
            WarehouseReceiptLine.Get(Rec."Document No.", Rec."Document Line No.");
            WarehouseReceiptLine.CalcFields("Total Package Quantity IPK");
            DocumentIsPurchase := true;
            Rec."Produced By" := Rec."Produced By"::" ";

        end;
        if Rec."Document No." = '' then
            DocumentNoEditable := true;
    end;



    var
        IpekProductionManagement: Codeunit "Ipek Production Management IPK";
        IpekPurchaseManagement: Codeunit "Ipek Purchase Management IPK";
        DocumentIsPurchase: Boolean;
        DocumentLineNoEditable: Boolean;
        DocumentNoEditable: Boolean;
        TotalPackageQuantity: Decimal;
        CannotGreaterErr: Label 'Total Package Quantity: %1 cannot be greater then Max Available Quantity: %2', Comment = '%1=TotalPackageQuantity; %2=MaxPackageQuantity';
}