codeunit 60014 "Item Price Dataset Mngt. 2 IPK"
{
    var
        ProcessingCompletedMsg: Label 'Processing completed for %1 items. %2 monthly records created, %3 records populated with cost data. Processing time: %4 minutes.', Comment = '%1 = Number of items, %2 = Number of monthly records, %3 = Number of populated records, %4 = Processing time in minutes';


    procedure ProcessAllItemsWithLedgerEntries()
    var
        Item: Record Item;
        ItemLedgerEntry: Record "Item Ledger Entry";
        ProcessingTimeMs: BigInteger;
        StartTime: DateTime;
        EndTime: DateTime;
        ProcessingTimeMinutes: Decimal;
        CreatedRecords: Integer;
        ItemCount: Integer;
        TotalCreated: Integer;
        TotalPopulated: Integer;
        VariantCode: Code[10];
        ItemVariantCombinations: Dictionary of [Text, Boolean];
        CombinationKey: Text;
    begin
        // Record start time
        StartTime := CurrentDateTime();

        // Initialize counters
        TotalCreated := 0;
        TotalPopulated := 0;
        ItemCount := 0;

        // First, collect all unique Item+Variant combinations from Item Ledger Entries
        // This eliminates redundant database queries and processes only combinations that have ledger entries
        ItemLedgerEntry.Reset();
        ItemLedgerEntry.SetCurrentKey("Item No.", "Variant Code");
        if ItemLedgerEntry.FindSet() then
            repeat
                // Only process non-blocked items
                if Item.Get(ItemLedgerEntry."Item No.") and (not Item.Blocked) then begin
                    CombinationKey := ItemLedgerEntry."Item No." + '|' + ItemLedgerEntry."Variant Code";
                    if not ItemVariantCombinations.ContainsKey(CombinationKey) then
                        ItemVariantCombinations.Add(CombinationKey, true);
                end;
            until ItemLedgerEntry.Next() = 0;

        // Process each unique Item+Variant combination
        foreach CombinationKey in ItemVariantCombinations.Keys() do
            // Parse the combination key to get Item No. and Variant Code
            if ParseItemVariantCombination(CombinationKey, Item."No.", VariantCode) then begin
                ItemCount += 1;

                // Create monthly structure for this specific item+variant combination
                CreatedRecords := CreateMonthlyStructureForItemVariant(Item."No.", VariantCode);
                TotalCreated += CreatedRecords;

                // Note: Cost data population is handled by CalculateCostForDatasetRecord
                // which is called during the monthly structure creation
            end;

        // Record end time and calculate duration
        EndTime := CurrentDateTime();
        ProcessingTimeMs := EndTime - StartTime;
        ProcessingTimeMinutes := ProcessingTimeMs / 60000; // Convert milliseconds to minutes

        // Show completion message with timing
        Message(ProcessingCompletedMsg,
            ItemCount, TotalCreated, TotalPopulated, Round(ProcessingTimeMinutes, 0.01));
    end;


    procedure ProcessSingleItemForTesting(ItemNo: Code[20])
    var
        Item: Record Item;
        ItemLedgerEntry: Record "Item Ledger Entry";
        DatasetRecord: Record "Item Price Dataset IPK";
        ProcessingTimeMs: BigInteger;
        StartTime: DateTime;
        EndTime: DateTime;
        ProcessingTimeMinutes: Decimal;
        CreatedRecords: Integer;
        PopulatedRecords: Integer;
        TotalCreated: Integer;
        VariantCodes: List of [Code[10]];
        VariantCode: Code[10];
        TestCompletedMsg: Label 'Test processing completed for item %1. %2 monthly records created, %3 records populated with cost data. Processing time: %4 minutes.', Comment = '%1 = Item No., %2 = Number of monthly records, %3 = Number of populated records, %4 = Processing time in minutes';
    begin
        // Record start time
        StartTime := CurrentDateTime();

        // Check if item exists and has ledger entries
        if not Item.Get(ItemNo) then begin
            Message('Item %1 does not exist.', ItemNo);
            exit;
        end;

        ItemLedgerEntry.SetRange("Item No.", ItemNo);
        if ItemLedgerEntry.IsEmpty() then begin
            Message('Item %1 has no Item Ledger Entries.', ItemNo);
            exit;
        end;

        // Clear existing dataset records for this item, but preserve manual prices
        // Only delete records without manual prices (both manual cost and price are 0)
        DatasetRecord.SetRange("Item No.", ItemNo);
        DatasetRecord.SetRange("Manual Unit Cost (ACY)", 0);
        DatasetRecord.SetRange("Manual Unit Price (ACY)", 0);
        if not DatasetRecord.IsEmpty() then
            DatasetRecord.DeleteAll(true);

        // Reset filters for further processing
        DatasetRecord.Reset();

        // Find all unique Variant Codes for this item in Item Ledger Entries
        ItemLedgerEntry.SetRange("Item No.", ItemNo);
        if ItemLedgerEntry.FindSet() then
            repeat
                if not VariantCodes.Contains(ItemLedgerEntry."Variant Code") then
                    VariantCodes.Add(ItemLedgerEntry."Variant Code");
            until ItemLedgerEntry.Next() = 0;

        // If no variants found, add empty variant
        if VariantCodes.Count() = 0 then
            VariantCodes.Add('');

        // Create monthly structure for each variant of this item
        TotalCreated := 0;
        foreach VariantCode in VariantCodes do begin
            CreatedRecords := CreateMonthlyStructureForItemVariant(ItemNo, VariantCode);
            TotalCreated += CreatedRecords;
        end;

        // Count populated records (cost calculation is done during structure creation)
        PopulatedRecords := 0;
        DatasetRecord.SetRange("Item No.", ItemNo);
        DatasetRecord.SetFilter("Average Unit Cost (ACY)", '<>%1', 0);
        PopulatedRecords := DatasetRecord.Count();

        // Record end time and calculate duration
        EndTime := CurrentDateTime();
        ProcessingTimeMs := EndTime - StartTime;
        ProcessingTimeMinutes := ProcessingTimeMs / 60000; // Convert milliseconds to minutes

        // Show completion message with timing
        Message(TestCompletedMsg,
            ItemNo, TotalCreated, PopulatedRecords, Round(ProcessingTimeMinutes, 0.01));
    end;


    local procedure CreateMonthlyStructureForItemVariant(ItemNo: Code[20]; VariantCode: Code[10]): Integer
    var
        Item: Record Item;
        DatasetRecord: Record "Item Price Dataset IPK";
        LastRecord: Record "Item Price Dataset IPK";
        ILE: Record "Item Ledger Entry";
        StartDate: Date;
        EndDate: Date;
        CreatedCount: Integer;
        MonthNo: Integer;
        NextEntryNo: Integer;
        MonthText: Text;
        CurrentMonth: Integer;
    begin
        // Get item description
        if not Item.Get(ItemNo) then
            exit(0);

        // Get current month
        CurrentMonth := WorkDate().Month();

        // Get next Entry No.
        if LastRecord.FindLast() then
            NextEntryNo := LastRecord."Entry No." + 1
        else
            NextEntryNo := 1;

        // Create December 2024 record first
        MonthText := '12.2024';
        StartDate := DMY2Date(1, 12, 2024); // First day of December 2024
        EndDate := CalcDate('<CM>', StartDate); // Last day of December 2024

        // Check if record already exists for December 2024
        DatasetRecord.Reset();
        DatasetRecord.SetRange("Item No.", ItemNo);
        DatasetRecord.SetRange("Variant Code", VariantCode);
        DatasetRecord.SetRange("Source Price Month", CopyStr(MonthText, 1, 7));

        if DatasetRecord.FindFirst() then begin
            // Record exists - check if it has manual prices
            if HasManualPrices(DatasetRecord) then begin
                // Update existing record with manual prices (preserve manual values)
                UpdateExistingRecordWithManualPrices(DatasetRecord);
            end else begin
                // No manual prices, update calculated values
                CalculateCostForDatasetRecord(DatasetRecord);
                CalculateSalesForDatasetRecord(DatasetRecord);
            end;
        end else begin
            // Create new dataset record for December 2024
            DatasetRecord.Init();
            DatasetRecord."Entry No." := NextEntryNo;
            DatasetRecord."Item No." := ItemNo;
            DatasetRecord."Variant Code" := VariantCode;
            DatasetRecord."Description" := Item.Description;
            DatasetRecord."Base Unit of Measure Code" := Item."Base Unit of Measure";
            DatasetRecord."Source Price Month" := CopyStr(MonthText, 1, 7);
            DatasetRecord."Start Date" := StartDate;
            DatasetRecord."End Date" := EndDate;
            DatasetRecord."Average Unit Cost (ACY)" := 0;
            DatasetRecord."Average Unit Cost (LCY)" := 0;

            DatasetRecord.Insert(true);

            // Calculate and set Quantity End of Month IPK using CalcSums
            ILE.Reset();
            ILE.SetRange("Item No.", DatasetRecord."Item No.");
            if DatasetRecord."Variant Code" <> '' then
                ILE.SetRange("Variant Code", DatasetRecord."Variant Code")
            else
                ILE.SetRange("Variant Code", '');
            ILE.SetFilter("Posting Date", '..%1', DatasetRecord."End Date");
            ILE.CalcSums(Quantity);
            DatasetRecord."Quantity End of Month IPK" := ILE.Quantity;
            DatasetRecord.Modify(true);

            // Calculate cost for the newly created dataset record
            CalculateCostForDatasetRecord(DatasetRecord);
            CalculateSalesForDatasetRecord(DatasetRecord);

            CreatedCount += 1;
            NextEntryNo += 1;
        end;

        // Create records for each month from January to current month in 2025
        // for the specific item+variant combination
        for MonthNo := 1 to CurrentMonth do begin
            // Format month as MM.YYYY
            if MonthNo < 10 then
                MonthText := '0' + Format(MonthNo) + '.2025'
            else
                MonthText := Format(MonthNo) + '.2025';

            // Calculate start and end dates for the month
            StartDate := DMY2Date(1, MonthNo, 2025); // First day of month
            EndDate := CalcDate('<CM>', StartDate); // Last day of month

            // Check if record already exists for this month
            DatasetRecord.Reset();
            DatasetRecord.SetRange("Item No.", ItemNo);
            DatasetRecord.SetRange("Variant Code", VariantCode);
            DatasetRecord.SetRange("Source Price Month", CopyStr(MonthText, 1, 7));

            if DatasetRecord.FindFirst() then begin
                // Record exists - check if it has manual prices
                if HasManualPrices(DatasetRecord) then begin
                    // Update existing record with manual prices (preserve manual values)
                    UpdateExistingRecordWithManualPrices(DatasetRecord);
                end else begin
                    // No manual prices, update calculated values
                    CalculateCostForDatasetRecord(DatasetRecord);
                    CalculateSalesForDatasetRecord(DatasetRecord);
                end;
            end else begin
                // Create new dataset record
                DatasetRecord.Init();
                DatasetRecord."Entry No." := NextEntryNo;
                DatasetRecord."Item No." := ItemNo;
                DatasetRecord."Variant Code" := VariantCode;
                DatasetRecord."Description" := Item.Description;
                DatasetRecord."Base Unit of Measure Code" := Item."Base Unit of Measure";
                DatasetRecord."Source Price Month" := CopyStr(MonthText, 1, 7);
                DatasetRecord."Start Date" := StartDate;
                DatasetRecord."End Date" := EndDate;
                DatasetRecord."Average Unit Cost (ACY)" := 0;
                DatasetRecord."Average Unit Cost (LCY)" := 0;

                DatasetRecord.Insert(true);

                // Calculate and set Quantity End of Month IPK using CalcSums
                ILE.Reset();
                ILE.SetRange("Item No.", DatasetRecord."Item No.");
                if DatasetRecord."Variant Code" <> '' then
                    ILE.SetRange("Variant Code", DatasetRecord."Variant Code")
                else
                    ILE.SetRange("Variant Code", '');
                ILE.SetFilter("Posting Date", '..%1', DatasetRecord."End Date");
                ILE.CalcSums(Quantity);
                DatasetRecord."Quantity End of Month IPK" := ILE.Quantity;
                DatasetRecord.Modify(true);

                // Calculate cost for the newly created dataset record
                CalculateCostForDatasetRecord(DatasetRecord);
                CalculateSalesForDatasetRecord(DatasetRecord);

                CreatedCount += 1;
                NextEntryNo += 1;
            end;
        end;

        exit(CreatedCount);
    end;

    local procedure GetACYCurrencyCode(): Code[10]
    var
        GeneralLedgerSetup: Record "General Ledger Setup";
    begin
        if GeneralLedgerSetup.Get() then
            exit(GeneralLedgerSetup."Additional Reporting Currency");

        exit('');
    end;


    local procedure GetPreviousMonthCostData(var DatasetRecord: Record "Item Price Dataset IPK"): Boolean
    var
        PreviousDatasetRecord: Record "Item Price Dataset IPK";
    begin
        // Look for dataset record with same item and variant for dates before current record
        PreviousDatasetRecord.Reset();
        PreviousDatasetRecord.SetRange("Item No.", DatasetRecord."Item No.");
        PreviousDatasetRecord.SetRange("Variant Code", DatasetRecord."Variant Code");
        PreviousDatasetRecord.SetFilter("Start Date", '<%1', DatasetRecord."Start Date");
        PreviousDatasetRecord.SetCurrentKey("Start Date");

        // Find all records and identify the one with the latest date that has cost data
        if PreviousDatasetRecord.FindLast() then begin
            DatasetRecord."Average Unit Cost (ACY)" := PreviousDatasetRecord."Average Unit Cost (ACY)";
            DatasetRecord."Average Unit Cost (LCY)" := PreviousDatasetRecord."Average Unit Cost (LCY)";
            DatasetRecord."Manual Unit Cost (ACY)" := PreviousDatasetRecord."Manual Unit Cost (ACY)";
            DatasetRecord."Currency Code" := PreviousDatasetRecord."Currency Code";
            DatasetRecord.Modify(true);
            exit(true); // Successfully found and applied previous month data
        end;

        exit(false); // No previous month data found
    end;

    local procedure CalculateCostForDatasetRecord(var DatasetRecord: Record "Item Price Dataset IPK"): Boolean
    var
        ValueEntry: Record "Value Entry";
        ACYCurrencyCode: Code[10];
        CostAmountACY: Decimal;
        CostAmountLCY: Decimal;
        TotalCostAmountACY: Decimal;
        TotalCostAmountLCY: Decimal;
        TotalQuantity: Decimal;
        WeightedAvgCostACY: Decimal;
        WeightedAvgCostLCY: Decimal;
    begin
        // Get ACY currency code
        ACYCurrencyCode := GetACYCurrencyCode();

        // Initialize totals
        TotalCostAmountACY := 0;
        TotalCostAmountLCY := 0;
        TotalQuantity := 0;

        // Filter Value Entries for this specific dataset record
        ValueEntry.Reset();
        ValueEntry.SetRange("Item No.", DatasetRecord."Item No.");

        // Handle Variant Code - if dataset has blank variant, filter for blank; otherwise use specific variant
        if DatasetRecord."Variant Code" = '' then
            ValueEntry.SetRange("Variant Code", '')
        else
            ValueEntry.SetRange("Variant Code", DatasetRecord."Variant Code");

        ValueEntry.SetRange("Posting Date", DatasetRecord."Start Date", DatasetRecord."End Date");

        // Filter for Purchase Invoice and Purchase Credit Memo document types
        ValueEntry.SetFilter("Document Type", '%1|%2',
            ValueEntry."Document Type"::"Purchase Invoice",
            ValueEntry."Document Type"::"Purchase Credit Memo");

        // Filter for Purchase and Output entry types
        ValueEntry.SetFilter("Item Ledger Entry Type", '%1|%2',
            ValueEntry."Item Ledger Entry Type"::Purchase,
            ValueEntry."Item Ledger Entry Type"::Output);
        ValueEntry.SetFilter("Entry Type", '<>%1', ValueEntry."Entry Type"::Rounding);
        ValueEntry.SetRange(Adjustment, false);

        // Collect cost data from Value Entries
        if ValueEntry.FindSet() then
            repeat
                // Get ACY amount - use ACY amount if available, otherwise fall back to LCY
                CostAmountACY := ValueEntry."Cost Amount (Actual) (ACY)";
                if CostAmountACY = 0 then
                    CostAmountACY := ValueEntry."Cost Amount (Actual)";

                CostAmountLCY := ValueEntry."Cost Amount (Actual)";

                // Accumulate amounts and quantities
                TotalCostAmountACY += CostAmountACY;
                TotalCostAmountLCY += CostAmountLCY;
                TotalQuantity += ValueEntry."Valued Quantity";
            until ValueEntry.Next() = 0;

        // Calculate weighted average costs if we have quantity
        if TotalQuantity <> 0 then begin
            WeightedAvgCostACY := TotalCostAmountACY / TotalQuantity;
            WeightedAvgCostLCY := TotalCostAmountLCY / TotalQuantity;

            // Update the dataset record
            DatasetRecord."Average Unit Cost (ACY)" := Abs(WeightedAvgCostACY);
            DatasetRecord."Average Unit Cost (LCY)" := Abs(WeightedAvgCostLCY);
            DatasetRecord."Currency Code" := ACYCurrencyCode;
            DatasetRecord.Modify(true);

            exit(true); // Successfully calculated and updated
        end;

        // If no current month data found, try to get data from previous months
        if GetPreviousMonthCostData(DatasetRecord) then
            exit(true); // Successfully used previous month data

        exit(false); // No data found from current or previous months
    end;

    local procedure CalculateSalesForDatasetRecord(var DatasetRecord: Record "Item Price Dataset IPK"): Boolean
    var
        ValueEntry: Record "Value Entry";
        CurrencyExchangeRate: Record "Currency Exchange Rate";
        ACYCurrencyCode: Code[10];
        SalesAmountLCY: Decimal;
        SalesAmountACY: Decimal;
        TotalSalesAmountLCY: Decimal;
        TotalSalesAmountACY: Decimal;
        TotalSalesQuantity: Decimal;
        WeightedAvgSalesPriceLCY: Decimal;
        WeightedAvgSalesPriceACY: Decimal;
    begin
        // Get ACY currency code
        ACYCurrencyCode := GetACYCurrencyCode();

        // Initialize totals
        TotalSalesAmountLCY := 0;
        TotalSalesAmountACY := 0;
        TotalSalesQuantity := 0;

        // Filter Value Entries for this specific dataset record
        ValueEntry.Reset();
        ValueEntry.SetRange("Item No.", DatasetRecord."Item No.");

        // Handle Variant Code - if dataset has blank variant, filter for blank; otherwise use specific variant
        if DatasetRecord."Variant Code" = '' then
            ValueEntry.SetRange("Variant Code", '')
        else
            ValueEntry.SetRange("Variant Code", DatasetRecord."Variant Code");

        ValueEntry.SetRange("Posting Date", DatasetRecord."Start Date", DatasetRecord."End Date");

        // Filter for Sales Invoice and Sales Credit Memo document types
        ValueEntry.SetFilter("Document Type", '%1|%2',
            ValueEntry."Document Type"::"Sales Invoice",
            ValueEntry."Document Type"::"Sales Credit Memo");

        // Filter for Sale entry type
        ValueEntry.SetFilter("Item Ledger Entry Type", '%1',
            ValueEntry."Item Ledger Entry Type"::Sale);
        ValueEntry.SetFilter("Entry Type", '<>%1', ValueEntry."Entry Type"::Rounding);
        ValueEntry.SetRange(Adjustment, false);

        // Collect sales data from Value Entries
        if ValueEntry.FindSet() then
            repeat
                SalesAmountLCY := ValueEntry."Sales Amount (Actual)";
                if ACYCurrencyCode <> '' then
                    SalesAmountACY := CurrencyExchangeRate.ExchangeAmtLCYToFCY(
                        ValueEntry."Posting Date",
                        ACYCurrencyCode,
                        SalesAmountLCY,
                        CurrencyExchangeRate.ExchangeRate(ValueEntry."Posting Date", ACYCurrencyCode))
                else
                    SalesAmountACY := SalesAmountLCY;
                TotalSalesAmountLCY += SalesAmountLCY;
                TotalSalesAmountACY += SalesAmountACY;
                TotalSalesQuantity += ValueEntry."Valued Quantity";
            until ValueEntry.Next() = 0;

        // Calculate weighted average sales price if we have quantity
        if TotalSalesQuantity <> 0 then begin
            WeightedAvgSalesPriceLCY := TotalSalesAmountLCY / TotalSalesQuantity;
            WeightedAvgSalesPriceACY := TotalSalesAmountACY / TotalSalesQuantity;

            // Update the dataset record
            DatasetRecord."Average Unit Price (LCY)" := Abs(WeightedAvgSalesPriceLCY);
            DatasetRecord."Average Unit Price (ACY)" := Abs(WeightedAvgSalesPriceACY);
            DatasetRecord."Currency Code" := ACYCurrencyCode;
            DatasetRecord.Modify(true);

            exit(true); // Successfully calculated and updated
        end;

        // If no current month data found, try to get data from previous months
        if GetPreviousMonthSalesData(DatasetRecord) then
            exit(true); // Successfully used previous month sales data

        exit(false); // No data found from current or previous months
    end;

    local procedure GetPreviousMonthSalesData(var DatasetRecord: Record "Item Price Dataset IPK"): Boolean
    var
        PreviousDatasetRecord: Record "Item Price Dataset IPK";
    begin
        // Look for dataset record with same item and variant for dates before current record
        PreviousDatasetRecord.Reset();
        PreviousDatasetRecord.SetRange("Item No.", DatasetRecord."Item No.");
        PreviousDatasetRecord.SetRange("Variant Code", DatasetRecord."Variant Code");
        PreviousDatasetRecord.SetFilter("Start Date", '<%1', DatasetRecord."Start Date");
        PreviousDatasetRecord.SetCurrentKey("Start Date");

        // Find all records and identify the one with the latest date that has sales price data
        if PreviousDatasetRecord.FindLast() then begin
            DatasetRecord."Average Unit Price (LCY)" := PreviousDatasetRecord."Average Unit Price (LCY)";
            DatasetRecord."Average Unit Price (ACY)" := PreviousDatasetRecord."Average Unit Price (ACY)";
            DatasetRecord."Manual Unit Price (ACY)" := PreviousDatasetRecord."Manual Unit Price (ACY)";
            DatasetRecord."Currency Code" := PreviousDatasetRecord."Currency Code";
            DatasetRecord.Modify(true);
            exit(true); // Successfully found and applied previous month sales data
        end;

        exit(false); // No previous month sales data found
    end;


    local procedure ParseItemVariantCombination(CombinationKey: Text; var ItemNo: Code[20]; var VariantCode: Code[10]): Boolean
    var
        SeparatorPos: Integer;
        ItemNoText: Text;
        VariantCodeText: Text;
    begin
        SeparatorPos := StrPos(CombinationKey, '|');
        if SeparatorPos = 0 then
            exit(false);

        ItemNoText := CopyStr(CombinationKey, 1, SeparatorPos - 1);
        VariantCodeText := CopyStr(CombinationKey, SeparatorPos + 1);

        // Ensure the text fits in the Code fields
        if StrLen(ItemNoText) > MaxStrLen(ItemNo) then
            exit(false);
        if StrLen(VariantCodeText) > MaxStrLen(VariantCode) then
            exit(false);

        ItemNo := CopyStr(ItemNoText, 1, MaxStrLen(ItemNo));
        VariantCode := CopyStr(VariantCodeText, 1, MaxStrLen(VariantCode));

        exit(true);
    end;

    procedure DrillDownCostValueEntries(DatasetRecord: Record "Item Price Dataset IPK")
    var
        ValueEntry: Record "Value Entry";
        ValueEntriesPage: Page "Value Entries";
    begin
        // Filter Value Entries for cost calculation
        ValueEntry.Reset();
        ValueEntry.SetRange("Item No.", DatasetRecord."Item No.");

        // Handle Variant Code - if dataset has blank variant, filter for blank; otherwise use specific variant
        if DatasetRecord."Variant Code" = '' then
            ValueEntry.SetRange("Variant Code", '')
        else
            ValueEntry.SetRange("Variant Code", DatasetRecord."Variant Code");

        ValueEntry.SetRange("Posting Date", DatasetRecord."Start Date", DatasetRecord."End Date");

        // Filter for Purchase Invoice and Purchase Credit Memo document types
        ValueEntry.SetFilter("Document Type", '%1|%2',
            ValueEntry."Document Type"::"Purchase Invoice",
            ValueEntry."Document Type"::"Purchase Credit Memo");

        // Filter for Purchase and Output entry types
        ValueEntry.SetFilter("Item Ledger Entry Type", '%1|%2',
            ValueEntry."Item Ledger Entry Type"::Purchase,
            ValueEntry."Item Ledger Entry Type"::Output);
        ValueEntry.SetFilter("Entry Type", '<>%1', ValueEntry."Entry Type"::Rounding);
        ValueEntry.SetRange(Adjustment, false);

        // Open filtered Value Entries page
        ValueEntriesPage.SetTableView(ValueEntry);
        ValueEntriesPage.Run();
    end;

    procedure DrillDownSalesValueEntries(DatasetRecord: Record "Item Price Dataset IPK")
    var
        ValueEntry: Record "Value Entry";
        ValueEntriesPage: Page "Value Entries";
    begin
        // Filter Value Entries for sales calculation
        ValueEntry.Reset();
        ValueEntry.SetRange("Item No.", DatasetRecord."Item No.");

        // Handle Variant Code - if dataset has blank variant, filter for blank; otherwise use specific variant
        if DatasetRecord."Variant Code" = '' then
            ValueEntry.SetRange("Variant Code", '')
        else
            ValueEntry.SetRange("Variant Code", DatasetRecord."Variant Code");

        ValueEntry.SetRange("Posting Date", DatasetRecord."Start Date", DatasetRecord."End Date");

        // Filter for Sales Invoice and Sales Credit Memo document types
        ValueEntry.SetFilter("Document Type", '%1|%2',
            ValueEntry."Document Type"::"Sales Invoice",
            ValueEntry."Document Type"::"Sales Credit Memo");

        // Filter for Sale entry type
        ValueEntry.SetFilter("Item Ledger Entry Type", '%1',
            ValueEntry."Item Ledger Entry Type"::Sale);
        ValueEntry.SetFilter("Entry Type", '<>%1', ValueEntry."Entry Type"::Rounding);
        ValueEntry.SetRange(Adjustment, false);

        // Open filtered Value Entries page
        ValueEntriesPage.SetTableView(ValueEntry);
        ValueEntriesPage.Run();
    end;

    local procedure HasManualPrices(DatasetRecord: Record "Item Price Dataset IPK"): Boolean
    begin
        exit((DatasetRecord."Manual Unit Cost (ACY)" <> 0) or (DatasetRecord."Manual Unit Price (ACY)" <> 0));
    end;

    local procedure UpdateExistingRecordWithManualPrices(var DatasetRecord: Record "Item Price Dataset IPK")
    var
        ILE: Record "Item Ledger Entry";
    begin
        // Update quantity calculation
        ILE.Reset();
        ILE.SetRange("Item No.", DatasetRecord."Item No.");
        if DatasetRecord."Variant Code" <> '' then
            ILE.SetRange("Variant Code", DatasetRecord."Variant Code")
        else
            ILE.SetRange("Variant Code", '');
        ILE.SetFilter("Posting Date", '..%1', DatasetRecord."End Date");
        ILE.CalcSums(Quantity);
        DatasetRecord."Quantity End of Month IPK" := ILE.Quantity;

        // Update calculated cost and price (preserve manual prices)
        CalculateCostForDatasetRecord(DatasetRecord);
        CalculateSalesForDatasetRecord(DatasetRecord);
        DatasetRecord.Modify(true);
    end;
}