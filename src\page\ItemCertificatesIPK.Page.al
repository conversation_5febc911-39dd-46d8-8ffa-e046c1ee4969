page 60008 "Item Certificates IPK"
{
    ApplicationArea = All;
    Caption = 'Item Certificates';
    PageType = List;
    SourceTable = "Item Certificate IPK";
    UsageCategory = Administration;
    DelayedInsert = true;
    layout
    {
        area(Content)
        {
            repeater(General)
            {
                field("Entry No."; Rec."Entry No.")
                {
                }
                field(Raw; Rec.Raw)
                {
                }
                field(Type; Rec."Type")
                {
                }
                field(Certificate; Rec.Certificate)
                {
                }
                field("Product Group"; Rec."Product Group")
                {
                }
                field("Certificate ID"; Rec."Certificate ID")
                {
                }
                field("Comment Line For Document"; Rec."Comment Line For Document")
                {
                }
            }
        }
    }
}