codeunit 60012 "Automatic Family Creation IPK"
{
    procedure CreateAutomaticFamilies()
    var
        TotalCreated: Integer;
        TotalErrors: Integer;
        TotalProcessed: Integer;
    begin
        ProcessItemsWithBOMs(TotalProcessed, TotalCreated, TotalErrors);
    end;

    procedure DeleteAllFamilies()
    var
        Family: Record Family;
        FamilyLine: Record "Family Line";
        ConfirmManagement: Codeunit "Confirm Management";
    begin
        if not ConfirmManagement.GetResponseOrDefault(DeleteAllFamiliesConfirmLbl, true) then
            exit;

        if not FamilyLine.IsEmpty() then
            FamilyLine.DeleteAll(true);

        if not Family.IsEmpty() then
            Family.DeleteAll(true);

        Message(AllFamiliesDeletedLbl);
    end;

    local procedure ProcessItemsWithBOMs(var TotalProcessed: Integer; var TotalCreated: Integer; var TotalErrors: Integer)
    var
        IpekPamukSetup: Record "Ipek Pamuk Setup IPK";
        ProductionBOMLine: Record "Production BOM Line";
        SourceItemNo: Code[20];
        YMQuantity: Decimal;
        ProcessedBOMs: List of [Code[20]];
        YMFilter: Text[50];
    begin
        TotalProcessed := 0;
        TotalCreated := 0;
        TotalErrors := 0;

        IpekPamukSetup.GetRecordOnce();
        YMFilter := IpekPamukSetup."Waterjet Item Category Filter";
        if YMFilter = '' then
            YMFilter := 'YM.01*';

        ProductionBOMLine.SetRange(Type, ProductionBOMLine.Type::Item);
        ProductionBOMLine.SetFilter("Item Category Code IPK", YMFilter);

        if not ProductionBOMLine.FindSet() then
            exit;

        Message(BOMLinesToProcessLbl, ProductionBOMLine.Count(), YMFilter);

        repeat
            // Get source item from Production BOM header
            SourceItemNo := GetSourceItemFromProductionBOM(ProductionBOMLine."Production BOM No.");

            if SourceItemNo <> '' then begin
                // Check if we haven't already processed this source item
                if not ProcessedBOMs.Contains(SourceItemNo) then begin
                    ProcessedBOMs.Add(SourceItemNo);
                    TotalProcessed += 1;

                    // Get total YM.01* quantity for this Production BOM
                    YMQuantity := GetYMQuantityFromBOM(ProductionBOMLine."Production BOM No.");

                    if YMQuantity > 0 then begin
                        // Create family record with 2 lines if it doesn't already exist
                        if CreateWaterjetFamilyRecord(SourceItemNo, YMQuantity) then
                            TotalCreated += 1;
                    end else
                        TotalErrors += 1;
                end;
            end else
                TotalErrors += 1;

        until ProductionBOMLine.Next() = 0;
    end;

    procedure GetSourceItemFromProductionBOM(ProductionBOMNo: Code[20]): Code[20]
    var
        Item: Record Item;
    begin
        // Find item that has this Production BOM assigned
        Item.SetRange("Production BOM No.", ProductionBOMNo);
        if Item.FindFirst() then
            exit(Item."No.")
        else
            exit('');
    end;

    procedure GetYMQuantityFromBOM(ProductionBOMNo: Code[20]): Decimal
    var
        IpekPamukSetup: Record "Ipek Pamuk Setup IPK";
        ProductionBOMLine: Record "Production BOM Line";
        TotalYMQuantity: Decimal;
        YMFilter: Text[50];
    begin
        TotalYMQuantity := 0;

        IpekPamukSetup.GetRecordOnce();
        YMFilter := IpekPamukSetup."Waterjet Item Category Filter";
        if YMFilter = '' then
            YMFilter := 'YM.01*';

        ProductionBOMLine.SetRange("Production BOM No.", ProductionBOMNo);
        ProductionBOMLine.SetRange(Type, ProductionBOMLine.Type::Item);
        ProductionBOMLine.SetFilter("Item Category Code IPK", YMFilter);

        if ProductionBOMLine.FindSet() then
            repeat
                TotalYMQuantity += ProductionBOMLine."Quantity per";
            until ProductionBOMLine.Next() = 0;

        exit(TotalYMQuantity);
    end;

    procedure CreateWaterjetFamilyRecord(ItemNo: Code[20]; WaterjetQuantity: Decimal): Boolean
    var
        CuttingRatioByType: Record "Cutting Ratio By Type IPK";
        Family: Record Family;
        FamilyLine: Record "Family Line";
        IpekPamukSetup: Record "Ipek Pamuk Setup IPK";
        Item: Record Item;
        ItemCategory: Record "Item Category";
        CuttingItemNo: Code[20];
        FamilyNo: Code[20];
        CuttingRatio: Decimal;
        FinalQuantity: Decimal;
        CuttingCategory: Enum "Cutting Category Type IPK";
        LineNo: Integer;
        FamilyDescription: Text[100];
    begin
        // Check if family already exists for this item
        FamilyNo := ItemNo;
        if Family.Get(FamilyNo) then
            exit(false); // Family already exists, skip this item

        if not Item.Get(ItemNo) then
            Error(ItemNotFoundErr, ItemNo);

        FamilyDescription := Item.Description;

        CuttingCategory := DetermineCuttingCategory(ItemNo);
        if CuttingCategory = CuttingCategory::" " then
            Error(NoCuttingCategoryErr, ItemNo, Item.Description);

        CuttingRatioByType.SetRange("Cutting Category Type", CuttingCategory);
        if not CuttingRatioByType.FindFirst() then
            Error(NoCuttingRatioErr, Format(CuttingCategory), ItemNo);

        CuttingRatio := CuttingRatioByType."Cutting Ratio";
        if CuttingRatio <= 0 then
            Error(InvalidCuttingRatioErr, CuttingRatio, Format(CuttingCategory), ItemNo);

        FinalQuantity := WaterjetQuantity * CuttingRatio;

        IpekPamukSetup.GetRecordOnce();
        ItemCategory.Get(Item."Item Category Code");
        case ItemCategory."Organic IPK" of
            ItemCategory."Organic IPK"::Yes:
                CuttingItemNo := IpekPamukSetup."Organic Cutting Item No.";
            ItemCategory."Organic IPK"::No:
                CuttingItemNo := IpekPamukSetup."Cutting Item No.";
            else
                CuttingItemNo := IpekPamukSetup."Cutting Item No.";
        end;



        if CuttingItemNo = '' then
            Error(NoCuttingItemSetupErr);

        if not Item.Get(CuttingItemNo) then
            Error(CuttingItemNotFoundErr, CuttingItemNo);


        if StrLen(FamilyDescription) > 100 then
            FamilyDescription := CopyStr(FamilyDescription, 1, 100);

        // Create Family header
        Family.Init();
        Family."No." := FamilyNo;
        Family.Description := FamilyDescription;
        if not Family.Insert(true) then
            Error(FailedToCreateFamilyHeaderErr, FamilyNo);

        // Create first family line with original item (quantity = 1)
        LineNo := 10000;
        FamilyLine.Init();
        FamilyLine."Family No." := FamilyNo;
        FamilyLine."Line No." := LineNo;
        FamilyLine.Validate("Item No.", ItemNo);
        if Item.Get(ItemNo) then
            FamilyLine.Description := Item.Description;
        FamilyLine.Quantity := 1;

        // Set variant code if item has variants
        SetVariantCodeIfAvailable(FamilyLine, ItemNo);

        if not FamilyLine.Insert(true) then
            Error(FailedToCreateFirstFamilyLineErr, ItemNo);

        // Create second family line with MK001585 item
        LineNo := 20000;
        FamilyLine.Init();
        FamilyLine."Family No." := FamilyNo;
        FamilyLine."Line No." := LineNo;
        FamilyLine.Validate("Item No.", CuttingItemNo);
        if Item.Get(CuttingItemNo) then
            FamilyLine.Description := Item.Description;
        FamilyLine.Quantity := FinalQuantity; // WaterjetQuantity * CuttingRatio

        // Set variant code if MK001585 has variants
        SetVariantCodeIfAvailable(FamilyLine, CuttingItemNo);

        if not FamilyLine.Insert(true) then
            Error(FailedToCreateSecondFamilyLineErr, CuttingItemNo, FamilyNo);

        exit(true);
    end;

    local procedure DetermineCuttingCategory(ItemNo: Code[20]): Enum "Cutting Category Type IPK"
    var
        Item: Record Item;
        CuttingCategory: Enum "Cutting Category Type IPK";
    begin
        if not Item.Get(ItemNo) then
            exit(CuttingCategory::" ");

        // First try to determine from Item Category Description
        Item.CalcFields("Item Category Description IPK");
        CuttingCategory := AnalyzeTextForCuttingCategory(Item."Item Category Description IPK");

        // If not found in category description, try item description
        if CuttingCategory = CuttingCategory::" " then
            CuttingCategory := AnalyzeTextForCuttingCategory(Item.Description);

        exit(CuttingCategory);
    end;

    local procedure AnalyzeTextForCuttingCategory(TextToAnalyze: Text): Enum "Cutting Category Type IPK"
    var
        CuttingCategory: Enum "Cutting Category Type IPK";
        UpperText: Text;
    begin
        if TextToAnalyze = '' then
            exit(CuttingCategory::" ");

        // Convert to uppercase for case-insensitive matching
        UpperText := UpperCase(TextToAnalyze);

        // Pattern matching based on Turkish keywords (priority order)
        // Check for YUVARLAK → Round (value 3)
        if (StrPos(UpperText, 'YUV') > 0) or (StrPos(UpperText, 'YUVARLAK') > 0) then
            exit(CuttingCategory::Round);

        // Check for KARE → Square (value 1)
        if StrPos(UpperText, 'KARE') > 0 then
            exit(CuttingCategory::Square);

        // Check for BEBEK → Baby (value 4)
        if StrPos(UpperText, 'BEBEK') > 0 then
            exit(CuttingCategory::Baby);

        // Check for OVAL → Oval (value 2)
        if StrPos(UpperText, 'OVAL') > 0 then
            exit(CuttingCategory::Oval);

        // No pattern matched
        exit(CuttingCategory::" ");
    end;

    local procedure SetVariantCodeIfAvailable(var FamilyLine: Record "Family Line"; ItemNo: Code[20])
    var
        ItemVariant: Record "Item Variant";
    begin
        ItemVariant.SetRange("Item No.", ItemNo);
        if ItemVariant.FindFirst() then
            FamilyLine."Variant Code IPK" := ItemVariant.Code;
    end;


    procedure GetProcessingStatistics(var ItemsWithBOMs: Integer; var ItemsWithYMBOMs: Integer; var ItemsWithExistingFamilies: Integer; var ItemsReadyForProcessing: Integer)
    var
        FamilyLine: Record "Family Line";
        Item: Record Item;
        ProductionBOMLine: Record "Production BOM Line";
        SourceItemNo: Code[20];
        ProcessedItems: List of [Code[20]];
    begin
        // Count items with Production BOMs
        Item.SetFilter("Production BOM No.", '<>%1', '');
        ItemsWithBOMs := Item.Count();

        // Count source items with YM.01* BOMs and existing families
        ItemsWithYMBOMs := 0;
        ItemsWithExistingFamilies := 0;

        ProductionBOMLine.SetRange(Type, ProductionBOMLine.Type::Item);
        ProductionBOMLine.SetFilter("Item Category Code IPK", 'YM.01*');

        if ProductionBOMLine.FindSet() then
            repeat
                SourceItemNo := GetSourceItemFromProductionBOM(ProductionBOMLine."Production BOM No.");
                if (SourceItemNo <> '') and (not ProcessedItems.Contains(SourceItemNo)) then begin
                    ProcessedItems.Add(SourceItemNo);
                    ItemsWithYMBOMs += 1;

                    // Check if source item already has family records
                    FamilyLine.SetRange("Item No.", SourceItemNo);
                    if not FamilyLine.IsEmpty() then
                        ItemsWithExistingFamilies += 1;
                end;
            until ProductionBOMLine.Next() = 0;

        // Calculate items ready for processing (YM items without existing families)
        ItemsReadyForProcessing := ItemsWithYMBOMs - ItemsWithExistingFamilies;
    end;

    procedure TestYMDetection(ProductionBOMNo: Code[20]): Text
    var
        SourceItemNo: Code[20];
        YMQuantity: Decimal;
    begin
        SourceItemNo := GetSourceItemFromProductionBOM(ProductionBOMNo);
        YMQuantity := GetYMQuantityFromBOM(ProductionBOMNo);

        if YMQuantity > 0 then
            exit(StrSubstNo(YmQuantityFoundMsg, YMQuantity, SourceItemNo))
        else
            exit(NoYmComponentsFoundMsg);
    end;

    procedure TestWaterjetDetection(ProductionBOMNo: Code[20]): Text
    begin
        exit(TestYMDetection(ProductionBOMNo));
    end;

    procedure TestPatternMatching(ItemNo: Code[20]): Text
    var
        CuttingCategory: Enum "Cutting Category Type IPK";
    begin
        CuttingCategory := DetermineCuttingCategory(ItemNo);
        if CuttingCategory = CuttingCategory::" " then
            exit(NoPatternMatchedMsg)
        else
            exit(StrSubstNo(MatchedCategoryMsg, Format(CuttingCategory)));
    end;

    procedure TestPatternMatchingText(TextToAnalyze: Text): Text
    var
        CuttingCategory: Enum "Cutting Category Type IPK";
    begin
        CuttingCategory := AnalyzeTextForCuttingCategory(TextToAnalyze);
        if CuttingCategory = CuttingCategory::" " then
            exit(NoPatternMatchedMsg)
        else
            exit(StrSubstNo(MatchedCategoryMsg, Format(CuttingCategory)));
    end;

    var
        AllFamiliesDeletedLbl: Label 'All families have been deleted successfully.';
        BOMLinesToProcessLbl: Label 'Found %1 Production BOM lines with %2 item categories to process', Comment = '%1 = Count of BOM lines, %2 = Item Category Filter';
        CuttingItemNotFoundErr: Label 'Required cutting item %1 not found', Comment = '%1 = Cutting Item No.';
        DeleteAllFamiliesConfirmLbl: Label 'Are you sure you want to delete all families? This action cannot be undone.';
        FailedToCreateFamilyHeaderErr: Label 'Failed to create family header %1', Comment = '%1 = Family No.';
        FailedToCreateFirstFamilyLineErr: Label 'Failed to create first family line for item %1', Comment = '%1 = Item No.';
        FailedToCreateSecondFamilyLineErr: Label 'Failed to create second family line for item %1 in family %2', Comment = '%1 = Item No., %2 = Family No.';
        InvalidCuttingRatioErr: Label 'Invalid cutting ratio %1 for category %2 (Item: %3)', Comment = '%1 = Cutting Ratio, %2 = Cutting Category, %3 = Item No.';
        ItemNotFoundErr: Label 'Item %1 not found', Comment = '%1 = Item No.';
        MatchedCategoryMsg: Label 'Matched category: %1', Comment = '%1 = Category Name';
        NoCuttingCategoryErr: Label 'Could not determine cutting category for item %1 (Description: %2)', Comment = '%1 = Item No., %2 = Item Description';
        NoCuttingItemSetupErr: Label 'No cutting item configured in Ipek Pamuk Setup';
        NoCuttingRatioErr: Label 'No cutting ratio configured for category %1 (Item: %2)', Comment = '%1 = Cutting Category, %2 = Item No.';
        NoPatternMatchedMsg: Label 'No pattern matched';
        NoYmComponentsFoundMsg: Label 'No YM.01* components found in Production BOM';
        YmQuantityFoundMsg: Label 'YM.01* quantity found: %1 for source item: %2', Comment = '%1 = Quantity, %2 = Source Item No.';
}