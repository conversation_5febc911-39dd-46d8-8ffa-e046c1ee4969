page 60009 "Item Certificate Line IPK"
{
    ApplicationArea = All;
    Caption = 'Item Certificate Line';
    PageType = List;
    SourceTable = "Item Certificate Line IPK";
    UsageCategory = None;

    layout
    {
        area(Content)
        {
            repeater(General)
            {

                field("Certificate Entry No."; Rec."Certificate Entry No.")
                {
                    ToolTip = 'Specifies the value of the Certificate Entry No. field.';
                }
                field(Raw; Rec.Raw)
                {
                    ToolTip = 'Specifies the value of the Raw field.';
                }
                field(Type; Rec."Type")
                {
                    ToolTip = 'Specifies the value of the Type field.';
                }
                field("Product Group"; Rec."Product Group")
                {
                    ToolTip = 'Specifies the value of the Product Group field.';
                }
                field("Certificate ID"; Rec."Certificate ID")
                {
                    ToolTip = 'Specifies the value of the Certificate ID field.';
                }
                field("Comment Line For Document"; Rec."Comment Line For Document")
                {
                    ToolTip = 'Specifies the value of the Comment Line For Document field.';
                }
            }
        }
    }
}