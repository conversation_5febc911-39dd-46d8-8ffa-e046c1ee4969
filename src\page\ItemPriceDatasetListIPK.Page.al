page 60032 "Item Price Dataset List IPK"
{
    ApplicationArea = All;
    Caption = 'Item Price Dataset';
    Extensible = false;
    PageType = List;
    SourceTable = "Item Price Dataset IPK";
    UsageCategory = Lists;
    Editable = true;

    layout
    {
        area(Content)
        {
            repeater(General)
            {
                field("Entry No."; Rec."Entry No.")
                {
                }
                field("Item No."; Rec."Item No.")
                {
                }
                field("Variant Code"; Rec."Variant Code")
                {
                }

                field(Description; Rec."Description")
                {
                }
                field("Source Price Month"; Rec."Source Price Month")
                {
                }
                field("Average Unit Cost (ACY)"; Rec."Average Unit Cost (ACY)")
                {
                    ToolTip = 'Specifies the calculated average unit cost. Click to view the underlying Value Entry records used for this calculation.';

                    trigger OnDrillDown()
                    var
                        ItemPriceDatasetMngt2: Codeunit "Item Price Dataset Mngt. 2 IPK";
                    begin
                        ItemPriceDatasetMngt2.DrillDownCostValueEntries(Rec);
                    end;
                }
                field("Manual Unit Cost (ACY)"; Rec."Manual Unit Cost (ACY)")
                {
                }
                field("Average Unit Price (ACY)"; Rec."Average Unit Price (ACY)")
                {
                    ToolTip = 'Specifies the calculated average unit price. Click to view the underlying Value Entry records used for this calculation.';

                    trigger OnDrillDown()
                    var
                        ItemPriceDatasetMngt2: Codeunit "Item Price Dataset Mngt. 2 IPK";
                    begin
                        ItemPriceDatasetMngt2.DrillDownSalesValueEntries(Rec);
                    end;
                }
                field("Manual Unit Price (ACY)"; Rec."Manual Unit Price (ACY)")
                {
                }
                field("Use Manual Price"; Rec."Use Manual Price")
                {
                }
                field("Average Unit Cost (LCY)"; Rec."Average Unit Cost (LCY)")
                {
                }
                field("Average Unit Price (LCY)"; Rec."Average Unit Price (LCY)")
                {
                }
                field("Start Date"; Rec."Start Date")
                {
                }
                field("End Date"; Rec."End Date")
                {
                }
                field("Quantity End of Month IPK"; Rec."Quantity End of Month IPK")
                {
                    ToolTip = 'Specifies the total quantity of the item at the end of the specified period. Click to view the underlying Item Ledger Entry records used for this calculation.';

                    trigger OnDrillDown()
                    var
                        ItemLedgerEntry: Record "Item Ledger Entry";
                        ItemLedgerEntriesPage: Page "Item Ledger Entries";
                    begin
                        if Rec."Item No." = '' then
                            Error(this.NoItemSelectedErrLbl);

                        ItemLedgerEntry.Reset();
                        ItemLedgerEntry.SetRange("Item No.", Rec."Item No.");
                        if Rec."Variant Code" <> '' then
                            ItemLedgerEntry.SetRange("Variant Code", Rec."Variant Code");
                        if Rec."End Date" <> 0D then
                            ItemLedgerEntry.SetFilter("Posting Date", '..%1', Rec."End Date");

                        ItemLedgerEntriesPage.SetTableView(ItemLedgerEntry);
                        ItemLedgerEntriesPage.Run();
                    end;
                }
                field("Base Unit of Measure Code"; Rec."Base Unit of Measure Code")
                {
                }
            }
        }
    }

    actions
    {
        area(Processing)
        {
            group(DataProcessing)
            {
                Caption = 'Data Processing';

                action("DeleteAll")
                {
                    ApplicationArea = All;
                    Caption = 'Delete All';
                    Image = Delete;
                    Promoted = true;
                    PromotedCategory = Process;
                    PromotedIsBig = true;
                    ToolTip = 'Delete all records from the Item Price Dataset table.';

                    trigger OnAction()
                    var
                        DatasetRecord: Record "Item Price Dataset IPK";
                        ManualPriceCount: Integer;
                        ConfirmMsg: Text;
                        DeleteOption: Integer;
                    begin
                        // Count records with manual prices
                        DatasetRecord.Reset();
                        DatasetRecord.SetFilter("Manual Unit Cost (ACY)", '<>%1', 0);
                        ManualPriceCount := DatasetRecord.Count();
                        
                        DatasetRecord.Reset();
                        DatasetRecord.SetFilter("Manual Unit Price (ACY)", '<>%1', 0);
                        ManualPriceCount += DatasetRecord.Count();
                        
                        if ManualPriceCount > 0 then begin
                            ConfirmMsg := StrSubstNo(ManualPricesWarningLbl, ManualPriceCount);
                            DeleteOption := StrMenu('Delete ALL records (manual prices will be LOST),Delete only calculated records (preserve manual prices),Cancel', 3, ConfirmMsg);
                            
                            case DeleteOption of
                                1: begin
                                    // Delete all records
                                    DatasetRecord.Reset();
                                    DatasetRecord.DeleteAll(true);
                                    Message('All records have been deleted, including %1 records with manual prices.', ManualPriceCount);
                                end;
                                2: begin
                                    // Delete only records without manual prices
                                    DatasetRecord.Reset();
                                    DatasetRecord.SetRange("Manual Unit Cost (ACY)", 0);
                                    DatasetRecord.SetRange("Manual Unit Price (ACY)", 0);
                                    DatasetRecord.DeleteAll(true);
                                    Message('Calculated records deleted. %1 records with manual prices were preserved.', ManualPriceCount);
                                end;
                                3:
                                    // Cancel - do nothing
                                    exit;
                            end;
                        end else
                            // No manual prices, proceed with normal deletion
                            if Confirm('Are you sure you want to delete ALL records from the Item Price Dataset? This action cannot be undone.') then begin
                                DatasetRecord.Reset();
                                DatasetRecord.DeleteAll(true);
                                Message('All records have been deleted.');
                            end;
                    end;
                }

                action("ClearCalculatedDataOnly")
                {
                    ApplicationArea = All;
                    Caption = 'Clear Calculated Data Only';
                    Image = ClearLog;
                    Promoted = true;
                    PromotedCategory = Process;
                    PromotedIsBig = true;
                    ToolTip = 'Clear only calculated average costs and prices, preserving all manual price entries.';

                    trigger OnAction()
                    var
                        DatasetRecord: Record "Item Price Dataset IPK";
                        ManualPriceCount: Integer;
                        DeletedCount: Integer;
                    begin
                        // Count records with manual prices (these will be preserved)
                        DatasetRecord.Reset();
                        DatasetRecord.SetFilter("Manual Unit Cost (ACY)", '<>%1', 0);
                        ManualPriceCount := DatasetRecord.Count();
                        
                        DatasetRecord.Reset();
                        DatasetRecord.SetFilter("Manual Unit Price (ACY)", '<>%1', 0);
                        ManualPriceCount += DatasetRecord.Count();

                        if Confirm('This will clear all calculated average costs and prices, but preserve %1 records with manual prices. Continue?', true, ManualPriceCount) then begin
                            // Delete only records without manual prices
                            DatasetRecord.Reset();
                            DatasetRecord.SetRange("Manual Unit Cost (ACY)", 0);
                            DatasetRecord.SetRange("Manual Unit Price (ACY)", 0);
                            DeletedCount := DatasetRecord.Count();
                            DatasetRecord.DeleteAll(true);
                            
                            // Clear calculated values from records with manual prices
                            DatasetRecord.Reset();
                            DatasetRecord.SetFilter("Manual Unit Cost (ACY)", '<>%1', 0);
                            if DatasetRecord.FindSet() then
                                repeat
                                    DatasetRecord."Average Unit Cost (ACY)" := 0;
                                    DatasetRecord."Average Unit Cost (LCY)" := 0;
                                    DatasetRecord."Average Unit Price (ACY)" := 0;
                                    DatasetRecord."Average Unit Price (LCY)" := 0;
                                    DatasetRecord.Modify(true);
                                until DatasetRecord.Next() = 0;

                            DatasetRecord.Reset();
                            DatasetRecord.SetFilter("Manual Unit Price (ACY)", '<>%1', 0);
                            if DatasetRecord.FindSet() then
                                repeat
                                    DatasetRecord."Average Unit Cost (ACY)" := 0;
                                    DatasetRecord."Average Unit Cost (LCY)" := 0;
                                    DatasetRecord."Average Unit Price (ACY)" := 0;
                                    DatasetRecord."Average Unit Price (LCY)" := 0;
                                    DatasetRecord.Modify(true);
                                until DatasetRecord.Next() = 0;

                            Message('Cleared calculated data from %1 records. Preserved %2 records with manual prices.', DeletedCount, ManualPriceCount);
                        end;
                    end;
                }

                action(ProcessAllItemsWithLedgerEntries)
                {
                    ApplicationArea = All;
                    Caption = 'Process All Items (New Method)';
                    Image = Calculate;
                    Promoted = true;
                    PromotedCategory = Process;
                    PromotedIsBig = true;
                    PromotedOnly = true;
                    ToolTip = 'Process cost dataset for all items with Item Ledger Entries using the new method (creates single records covering both cost and price perspective).';

                    trigger OnAction()
                    var
                        ItemPriceDatasetMngt2: Codeunit "Item Price Dataset Mngt. 2 IPK";
                    begin
                        if Confirm('This will process cost dataset for all items with Item Ledger Entries using the new method. Continue?') then
                            ItemPriceDatasetMngt2.ProcessAllItemsWithLedgerEntries();
                    end;
                }
                action(ProcessSingleItemTest)
                {
                    ApplicationArea = All;
                    Caption = 'Process Single Item for Testing';
                    Image = TestDatabase;
                    Promoted = true;
                    PromotedCategory = Process;
                    PromotedIsBig = false;
                    PromotedOnly = true;
                    ToolTip = 'Select an item to process for testing purposes.';

                    trigger OnAction()
                    var
                        ItemRec: Record Item;
                        ItemPriceDatasetMngt2: Codeunit "Item Price Dataset Mngt. 2 IPK";
                        ItemList: Page "Item List";
                    begin
                        ItemList.LookupMode(true);
                        if ItemList.RunModal() = ACTION::LookupOK then begin
                            ItemList.SetSelectionFilter(ItemRec);
                            if ItemRec.FindFirst() then begin
                                if Confirm(StrSubstNo(this.ProcessSingleItemTestConfirmLbl, ItemRec."No.")) then
                                    ItemPriceDatasetMngt2.ProcessSingleItemForTesting(ItemRec."No.");
                            end else
                                Message('No item was selected.');
                        end else
                            Message('Item selection cancelled.');
                    end;
                }
            }

            group(Related)
            {
                Caption = 'Related';

                action(ShowValueEntries)
                {
                    ApplicationArea = All;
                    Caption = 'Value Entries (Sales)';
                    Image = ValueLedger;
                    Promoted = true;
                    PromotedCategory = Report;
                    PromotedIsBig = true;
                    PromotedOnly = true;
                    ToolTip = 'View the value entries (sales invoices and credit memos) that were used to calculate the average price for this dataset record.';

                    trigger OnAction()
                    var
                        ValueEntry: Record "Value Entry";
                        ValueEntriesPage: Page "Value Entries";
                    begin
                        if Rec."Item No." = '' then
                            Error(this.NoItemSelectedErrLbl);

                        ValueEntry.SetRange("Item No.", Rec."Item No.");
                        if Rec."Variant Code" <> '' then
                            ValueEntry.SetRange("Variant Code", Rec."Variant Code");
                        if (Rec."Start Date" <> 0D) and (Rec."End Date" <> 0D) then
                            ValueEntry.SetRange("Posting Date", Rec."Start Date", Rec."End Date");
                        // Filter for Sale entry type
                        ValueEntry.SetRange("Item Ledger Entry Type", ValueEntry."Item Ledger Entry Type"::Sale);
                        // Filter for Sales Invoice and Sales Credit Memo document types
                        ValueEntry.SetFilter("Document Type", '%1|%2',
                            ValueEntry."Document Type"::"Sales Invoice",
                            ValueEntry."Document Type"::"Sales Credit Memo");
                        ValueEntry.SetFilter("Entry Type", '<>%1', ValueEntry."Entry Type"::Rounding);


                        ValueEntriesPage.SetTableView(ValueEntry);
                        ValueEntriesPage.Run();
                    end;
                }
                action(ShowPurchaseValueEntries)
                {
                    ApplicationArea = All;
                    Caption = 'Value Entries (Purchase)';
                    Image = Purchase;
                    Promoted = true;
                    PromotedCategory = Report;
                    PromotedIsBig = true;
                    PromotedOnly = true;
                    ToolTip = 'View the value entries (purchase invoices, credit memos, and adjustments) that were used to calculate the average cost for this dataset record.';

                    trigger OnAction()
                    var
                        ValueEntry: Record "Value Entry";
                        ValueEntriesPage: Page "Value Entries";
                    begin
                        if Rec."Item No." = '' then
                            Error(this.NoItemSelectedErrLbl);

                        ValueEntry.SetRange("Item No.", Rec."Item No.");
                        if Rec."Variant Code" <> '' then
                            ValueEntry.SetRange("Variant Code", Rec."Variant Code");
                        if (Rec."Start Date" <> 0D) and (Rec."End Date" <> 0D) then
                            ValueEntry.SetRange("Posting Date", Rec."Start Date", Rec."End Date");
                        // Filter for Purchase, Positive Adj, Negative Adj entry types
                        ValueEntry.SetFilter("Item Ledger Entry Type", '%1|%2|%3|%4',
                            ValueEntry."Item Ledger Entry Type"::Purchase,
                            ValueEntry."Item Ledger Entry Type"::"Positive Adjmt.",
                            ValueEntry."Item Ledger Entry Type"::"Negative Adjmt.",
                            ValueEntry."Item Ledger Entry Type"::Output);
                        // Filter for Purchase Invoice and Purchase Credit Memo document types
                        ValueEntry.SetFilter("Document Type", '%1|%2',
                            ValueEntry."Document Type"::"Purchase Invoice",
                            ValueEntry."Document Type"::"Purchase Credit Memo");
                        ValueEntry.SetFilter("Entry Type", '<>%1', ValueEntry."Entry Type"::Rounding);

                        ValueEntry.SetRange(Adjustment, false);


                        ValueEntriesPage.SetTableView(ValueEntry);
                        ValueEntriesPage.Run();
                    end;
                }
            }
        }
    }


    var
        NoItemSelectedErrLbl: Label 'No item selected.';
        ProcessSingleItemTestConfirmLbl: Label 'This will process cost dataset for item %1 only for testing. Continue?', Comment = '%1 = Item No.';
        ManualPricesWarningLbl: Label 'WARNING: %1 records contain manual price entries.', Comment = '%1 = Number of records with manual prices';
}
