codeunit 60003 "Ipek Purchase Management IPK"
{
    SingleInstance = true;
    procedure CreatePackageFromWarehouseReceiptLine(WarehouseReceiptLine: Record "Warehouse Receipt Line"; PackageCreationMethod: Enum "Package Creation Method IPK")
    var
        Item: Record Item;
        TempPackageCreation: Record "Package Creation IPK" temporary;
        QualityDocumentErr: Label 'You cannot create package without process the corresponding lines quality control document';
    begin
        Item.Get(WarehouseReceiptLine."Item No.");
        WarehouseReceiptLine.CalcFields("Total Package Quantity IPK");
        if (WarehouseReceiptLine."Quality Control Doc. Stat. IPK" = WarehouseReceiptLine."Quality Control Doc. Stat. IPK"::" ") or
         (WarehouseReceiptLine."Quality Control Doc. Stat. IPK" = WarehouseReceiptLine."Quality Control Doc. Stat. IPK"::"Input Pending") then
            Error(QualityDocumentErr);
        TempPackageCreation.Init();
        TempPackageCreation."Source Type" := TempPackageCreation."Source Type"::Purchase;
        TempPackageCreation."Document No." := WarehouseReceiptLine."No.";
        TempPackageCreation."Document Line No." := WarehouseReceiptLine."Line No.";
        TempPackageCreation.Validate("Item No.", WarehouseReceiptLine."Item No.");
        TempPackageCreation.Validate("Variant Code", WarehouseReceiptLine."Variant Code");
        TempPackageCreation."Lot No." := WarehouseReceiptLine."Lot No. IPK";
        TempPackageCreation."Creation Method" := PackageCreationMethod;

        TempPackageCreation.Validate("Item Pieces Of Pallet", Item."Pieces on Pallet IPK");
        TempPackageCreation.Validate("Order Quantity", WarehouseReceiptLine.Quantity);
        TempPackageCreation.Validate("Max Available Quantity", WarehouseReceiptLine.Quantity - WarehouseReceiptLine."Total Package Quantity IPK");

        if PackageCreationMethod = PackageCreationMethod::Single then
            TempPackageCreation."Package Count" := 1;
        TempPackageCreation.Insert(false);

        Page.Run(Page::"Package Creation IPK", TempPackageCreation);
    end;

    procedure CreateWarehouseReceiptLineDetailsFromPackageCreation(var TempPackageCreation: Record "Package Creation IPK" temporary)
    var
        i: Integer;

    begin
        TempPackageCreation.TestField("Package Count");
        TempPackageCreation.TestField("Package Quantity");
        TempPackageCreation."Produced By" := TempPackageCreation."Produced By"::" ";
        case TempPackageCreation."Creation Method" of
            TempPackageCreation."Creation Method"::Single:
                InsertWarehouseReceiptLineDetail(TempPackageCreation);
            TempPackageCreation."Creation Method"::Multiple:
                for i := 1 to TempPackageCreation."Package Count" do
                    InsertWarehouseReceiptLineDetail(TempPackageCreation);
        end;

        GlobalPackageNoInformation.MarkedOnly(true);
        Commit();//
        Report.Run(Report::"Palette Label IPK", true, true, GlobalPackageNoInformation);
        Clear(GlobalPackageNoInformation);
    end;

    local procedure InsertWarehouseReceiptLineDetail(var TempPackageCreation: Record "Package Creation IPK" temporary)
    var
        InventorySetup: Record "Inventory Setup";
        WarehouseReceiptLineDtl: Record "Warehouse Receipt Line Dtl IPK";
        NoSeries: Codeunit "No. Series";
    begin
        InventorySetup.GetRecordOnce();
        InventorySetup.TestField("Package Nos.");

        WarehouseReceiptLineDtl.Init();
        WarehouseReceiptLineDtl."Document No." := TempPackageCreation."Document No.";
        WarehouseReceiptLineDtl."Document Line No." := TempPackageCreation."Document Line No.";
        WarehouseReceiptLineDtl."Package No." := NoSeries.GetNextNo(InventorySetup."Package Nos.");
        WarehouseReceiptLineDtl.Validate("Item No.", TempPackageCreation."Item No.");
        WarehouseReceiptLineDtl.Validate("Variant Code", TempPackageCreation."Variant Code");
        WarehouseReceiptLineDtl."Lot No." := TempPackageCreation."Lot No.";
        WarehouseReceiptLineDtl.Quantity := TempPackageCreation."Package Quantity";
        WarehouseReceiptLineDtl.Insert(true);

        CreatePackageNoInformationFromWareHouseReciptLineDetail(WarehouseReceiptLineDtl);
    end;

    local procedure AssignItemTrackingInformation(LotNo: Code[50]; PackageNo: Code[50]; var PurchaseLine: Record "Purchase Line"; AvailabilityDate: Date; QtyToReceiveBase: Decimal; QtyToReceive: Decimal/*; QtyReceivedBase: Decimal*/)
    var
        TempSourceTrackingSpecification: Record "Tracking Specification" temporary;
        TempTrackingSpecification: Record "Tracking Specification" temporary;
        PurchLineReserve: Codeunit "Purch. Line-Reserve";
        ItemTrackingLines: Page "Item Tracking Lines";

    begin
        PurchLineReserve.InitFromPurchLine(TempSourceTrackingSpecification, PurchaseLine);

        TempTrackingSpecification.Init();
        TempTrackingSpecification."Lot No." := LotNo;
        TempTrackingSpecification."Package No." := PackageNo;

        TempTrackingSpecification.SetQuantities(QtyToReceiveBase,
                                                QtyToReceive,
                                                QtyToReceiveBase,
                                                0,
                                                0,
                                                0,
                                                0);
        TempTrackingSpecification.Insert(false);

        ItemTrackingLines.SetBlockCommit(true);
        OverrideQuantityHandled := true;
        ItemTrackingLines.RegisterItemTrackingLines(TempSourceTrackingSpecification, AvailabilityDate, TempTrackingSpecification);
        OverrideQuantityHandled := false;
    end;

    [EventSubscriber(ObjectType::Page, Page::"Item Tracking Lines", OnAfterCopyTrackingSpec, '', false, false)]
    local procedure "Item Tracking Lines_OnAfterCopyTrackingSpec"(var SourceTrackingSpec: Record "Tracking Specification"; var DestTrkgSpec: Record "Tracking Specification")
    begin
        if OverrideQuantityHandled then
            DestTrkgSpec."Quantity Handled (Base)" := SourceTrackingSpec."Quantity Handled (Base)";

    end;

    procedure AssignItemTrackingInformationFromWarehouseReceiptLine(WarehouseReceiptLine: Record "Warehouse Receipt Line")
    var
        PurchaseHeader: Record "Purchase Header";
        PurchaseLine: Record "Purchase Line";
    begin
        PurchaseLine.Get(WarehouseReceiptLine."Source Subtype", WarehouseReceiptLine."Source No.", WarehouseReceiptLine."Source Line No.");
        PurchaseHeader.Get(PurchaseLine."Document Type", PurchaseLine."Document No.");

        AssignItemTrackingInformation(WarehouseReceiptLine."Lot No. IPK", '', PurchaseLine, PurchaseHeader."Posting Date", WarehouseReceiptLine."Qty. to Receive (Base)", WarehouseReceiptLine."Qty. to Receive"/*, WarehouseReceiptLine."Qty. Received (Base)"*/);

    end;

    procedure AssignItemTrackingInformationFromWarehouseReceiptLineDetail(WarehouseReceiptLineDtl: Record "Warehouse Receipt Line Dtl IPK")
    var
        PurchaseHeader: Record "Purchase Header";
        PurchaseLine: Record "Purchase Line";
        WarehouseReceiptLine: Record "Warehouse Receipt Line";
    begin
        WarehouseReceiptLine.Get(WarehouseReceiptLineDtl."Document No.", WarehouseReceiptLineDtl."Document Line No.");
        PurchaseLine.Get(WarehouseReceiptLine."Source Subtype", WarehouseReceiptLine."Source No.", WarehouseReceiptLine."Source Line No.");
        PurchaseHeader.Get(PurchaseLine."Document Type", PurchaseLine."Document No.");

        AssignItemTrackingInformation(WarehouseReceiptLineDtl."Lot No.", WarehouseReceiptLineDtl."Package No.", PurchaseLine, PurchaseHeader."Posting Date", WarehouseReceiptLineDtl.Quantity, WarehouseReceiptLineDtl.Quantity/*, 0*/);
    end;


    procedure AssignItemTrackingInformationFromWarehouseReceiptHeader(WarehouseReceiptHeader: Record "Warehouse Receipt Header")
    var
        WarehouseReceiptLine: Record "Warehouse Receipt Line";
        ItemTrackAssignedMsg: Label 'Item Tracking Information are assigned.';
    begin
        WarehouseReceiptLine.SetRange("No.", WarehouseReceiptHeader."No.");
        WarehouseReceiptLine.SetRange("Item Tracking Info Assignd IPK", false);
        WarehouseReceiptLine.FindSet(true);

        repeat
            WarehouseReceiptLine.CalcFields("Total Package Quantity IPK");
            WarehouseReceiptLine.Validate("Qty. to Receive", WarehouseReceiptLine."Total Package Quantity IPK");

            if WarehouseReceiptLine."Qty. to Receive" > 0 then begin
                AssignItemTrackingInformationFromWarehouseReceiptLine(WarehouseReceiptLine);

                WarehouseReceiptLine.Validate("Item Tracking Info Assignd IPK", true);

                WarehouseReceiptLine.Modify(true);
            end;
        until WarehouseReceiptLine.Next() = 0;

        Message(ItemTrackAssignedMsg);
    end;

    procedure CreatePackageNoInformationFromWareHouseReciptLineDetail(WarehouseReceiptLineDtl: Record "Warehouse Receipt Line Dtl IPK")
    var
        Item: Record Item;
        PackageNoInfoLine: Record "Package No. Info. Line IPK";
        PackageNoInformation: Record "Package No. Information";
        WarehouseReceiptLine: Record "Warehouse Receipt Line";
    begin
        WarehouseReceiptLine.Get(WarehouseReceiptLineDtl."Document No.", WarehouseReceiptLineDtl."Document Line No.");

        PackageNoInformation.Init();
        PackageNoInformation."Package No." := WarehouseReceiptLineDtl."Package No.";
        PackageNoInformation.Validate("Location Code IPK", WarehouseReceiptLine."Location Code");
        PackageNoInformation.Validate("Document No. IPK", WarehouseReceiptLine."No.");
        PackageNoInformation.Validate("Created At IPK", CurrentDateTime());
        PackageNoInformation.Insert(true);

        PackageNoInfoLine.Init();
        PackageNoInfoLine."Package No." := PackageNoInformation."Package No.";
        PackageNoInfoLine.Insert(true);
        PackageNoInfoLine.Validate("Line Item No.", WarehouseReceiptLineDtl."Item No.");
        PackageNoInfoLine.Validate("Line Variant Code", WarehouseReceiptLineDtl."Variant Code");
        PackageNoInfoLine.Validate(Description, WarehouseReceiptLineDtl."Item Description");
        PackageNoInfoLine.Validate(Quantity, WarehouseReceiptLineDtl."Quantity");
        PackageNoInfoLine.Validate("Lot No.", WarehouseReceiptLineDtl."Lot No.");

        Item.Get(PackageNoInfoLine."Line Item No.");
        PackageNoInfoLine.Validate("Unit of Measure Code", Item."Base Unit of Measure");
        PackageNoInfoLine.Modify(true);

        GlobalPackageNoInformation := PackageNoInformation;
        GlobalPackageNoInformation.Mark(true);
    end;

    procedure WareHouseRecipt_VendorShipmentNo_OnAfterValidate(Rec: Record "Warehouse Receipt Header")
    var
        QualityControlHeaderQCM: Record "Quality Control Header QCM";
        WarehouseReceiptHeader2: Record "Warehouse Receipt Header";
        VendorShipmentNoInUseErr: Label 'This Vendor Shipment No: %1 already in use with Warehouse Recipt: %2. Please enter antother vendor shipment no', Comment = '%1 = Vendor Shipment No.; %2 = Warehouse Receipt No.';
    begin

        WarehouseReceiptHeader2.SetRange("Vendor Shipment No.", Rec."Vendor Shipment No.");
        // WarehouseReceiptHeader2.SetRange("Vendor No. IPK", Rec."Vendor No. IPK");
        WarehouseReceiptHeader2.SetFilter("No.", '<>%1', Rec."No.");

        if not WarehouseReceiptHeader2.IsEmpty() then
            Error(VendorShipmentNoInUseErr, Rec."Vendor Shipment No.", WarehouseReceiptHeader2."No.");

        QualityControlHeaderQCM.SetRange("Source Document No. IPK", Rec."No.");
        QualityControlHeaderQCM.ModifyAll("External Document No. IPK", Rec."Vendor Shipment No.", true);
    end;

    procedure UpdateLocationCodeForReceive(var PurchLine: Record "Purchase Line")
    var
        ItemCategory: Record "Item Category";
    begin
        if not ItemCategory.Get(PurchLine."Item Category Code") then
            exit;
        if ItemCategory."Default Receive Location IPK" = '' then
            exit;

        PurchLine."Location Code" := ItemCategory."Default Receive Location IPK";
    end;

    var
        GlobalPackageNoInformation: Record "Package No. Information";
        OverrideQuantityHandled: Boolean;
}