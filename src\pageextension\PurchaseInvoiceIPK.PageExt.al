pageextension 60058 "Purchase Invoice IPK" extends "Purchase Invoice"
{
    layout
    {
        addafter("Vendor Invoice No.")
        {
            field("VAT Registration No. IPK"; Rec."VAT Registration No.")
            {
                ApplicationArea = All;
                ToolTip = 'Specifies VAT Registration No.';
            }
        }
        addafter("Payment Method Code")
        {
            field("Bal. Account Type IPK"; Rec."Bal. Account Type")
            {
                ApplicationArea = All;
                ToolTip = 'Specifies the balancing account type.';
            }
            field("Bal. Account No. IPK"; Rec."Bal. Account No.")
            {
                ApplicationArea = All;
                ToolTip = 'Specifies the balancing account number.';
            }
        }
        // modify("Vendor Invoice No.")
        // {
        //     Visible = true;
        //     ApplicationArea = all;
        // }
    }
}