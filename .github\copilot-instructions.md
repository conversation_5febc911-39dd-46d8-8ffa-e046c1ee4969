# Ipek Pamuk Customization - Business Central AL Extension

## Project Overview
This is a Business Central AL extension for İpek Pamuk textile company, providing specialized manufacturing, inventory, and quality control functionality. The app extends core BC tables and adds custom business logic for production order management, lot tracking, package handling, and quality control workflows.

## Architecture & Patterns

### Naming Conventions
- **All objects must end with "IPK" suffix** (company identifier)
- Objects use descriptive English names with standard BC abbreviations
- Files follow pattern: `<ObjectName>IPK.<Type>.al` (e.g., `ProductionOrderIPK.TableExt.al`)
- Variables: PascalCase with record type included (e.g., `ProductionOrder`, `TempItemLedgerEntry`)
- Max object name length: 30 characters (including IPK suffix)

### Object ID Ranges
- Tables: 60000+ range
- Pages: 60000+ range  
- Codeunits: 60000+ range
- Extensions use company-specific ranges per object type

### Core Management Codeunits
Central business logic is organized in domain-specific codeunits:
- `IpekProductionManagementIPK` (60000): Production orders, lot numbers, package creation
- `IpekSalesManagementIPK` (60002): Sales processing, shipment handling
- `IpekPurchaseManagementIPK` (60003): Purchase workflows
- `IpekQualityManagementIPK` (60005): Quality control processes
- `IpekPackageTransMgtIPK` (60006): Package transfers and inventory adjustments

### Table Extensions Pattern
Every BC table extension follows this structure:
```al
tableextension 60XXX "TableName IPK" extends "Original Table"
{
    fields
    {
        field(60000; "Custom Field IPK"; DataType)
        {
            Caption = 'Custom Field IPK';
            DataClassification = CustomerContent; // or ToBeClassified
        }
    }
}
```

### Permission Management
- Single comprehensive permission set: `IpekPamukCustIPK.PermissionSet.al`
- Grants RIMD rights to all custom tables and objects
- Updated whenever new objects are added

## Key Business Domains

### Production & Manufacturing
- **Source Lot Tracking**: Critical feature linking consumption entries to output lot numbers
- **Package Creation**: Custom workflow for creating packages from production orders
- **Production Order Details**: Extended with custom fields for tracking production state
- **Lot Number Management**: Automatic lot number generation and tracking

### Inventory & Warehousing  
- **Bin Management**: Location-specific inventory handling
- **Inventory Adjustments**: Custom adjustment workflows with approval processes
- **Package Transfers**: Moving packages between locations with detailed tracking
- **Warehouse Receipt Extensions**: Additional fields for detailed receiving

### Quality Control
- **Quality Control Integration**: Extends existing QCM module functionality  
- **Item Certificates**: Certificate management for items with expiration tracking
- **Multiple QC Approvals**: Workflow for items requiring multiple quality approvals

### Sales & Shipping
- **Load Planning**: Planning shipments with load optimization
- **Customs Documentation**: Integration with customs officer records
- **Freight Management**: Item charge handling for freight costs

## Development Workflow

### VS Code Configuration
- Uses AL extension with Business Central LinterCop analyzer
- Root namespace: "Infotek"
- Turkish localization support (TRK language)
- Auto-reorganization on save with CRS extension
- Ruleset: `infotek.ruleset.json`

### Build & Deployment
- Sandbox environment deployment via launch.json
- StartupObjectId: 60071 (Source Lot No. Migration page)
- Schema update mode: ForceSync for development
- Multiple environment configurations available

### Translation Management
- XLIFF files in `/Translations/` folder
- Generated file: `Ipek Pamuk Customization.g.xlf` 
- Localized to Turkish (tr-TR)
- Use translation tools for maintaining XLIFF files

## Critical Migration Features
The app includes a sophisticated **Source Lot No. Migration** system:
- **Purpose**: Populate `Source Lot No. IPK` field in historical Item Ledger Entries
- **Logic**: Links consumption entries to related output entries using proximity algorithms
- **Entry Point**: Page 60071 "Source Lot No. Migration IPK"
- **Implementation**: Codeunit 60070 with multiple matching strategies

## Code Quality Standards
- Follow patterns in `USER_GUIDELINES.md` for AL best practices
- Use SingleInstance = true for management codeunits where appropriate
- Implement proper error handling with meaningful user messages
- Maintain consistent field validation and data classification
- Use flowfields sparingly for performance

## Common Integration Points
- **Item Ledger Entries**: Extended with Source Lot No. tracking
- **Production Orders**: Extended with last production date tracking  
- **Warehouse Documents**: Extended with custom fields for detailed tracking
- **Price Management**: Custom price dataset management for complex pricing
- **No. Series**: Heavy usage for document numbering across all modules

When modifying this codebase, always maintain the IPK suffix convention, follow the established architectural patterns, and ensure new objects are added to the permission set.
