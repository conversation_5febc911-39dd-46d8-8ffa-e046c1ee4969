page 60073 "Item Price Dataset By Loc IPK"
{
    ApplicationArea = All;
    Caption = 'Item Price Dataset By Location';
    PageType = List;
    SourceTable = "Item Price Dataset By Loc IPK";
    //UsageCategory = Lists;
    Editable = true;

    layout
    {
        area(Content)
        {
            repeater(General)
            {
                field("Entry No."; Rec."Entry No.")
                {
                }
                field("Item No."; Rec."Item No.")
                {
                }
                field("Variant Code"; Rec."Variant Code")
                {
                }
                field("Location Code"; Rec."Location Code")
                {
                }
                field("Location Name"; Rec."Location Name")
                {
                }
                field(Description; Rec."Description")
                {
                }
                field("Source Price Month"; Rec."Source Price Month")
                {
                    ToolTip = 'Specifies the reference month for price calculation in MM.YYYY format.';
                }
                field("Average Unit Cost (ACY)"; Rec."Average Unit Cost (ACY)")
                {
                    ToolTip = 'Specifies the calculated average unit cost. Click to view the underlying Value Entry records used for this calculation.';

                    trigger OnDrillDown()
                    var
                        ItemPriceDatasetByLocMgt: Codeunit "Item Price Dataset By Loc IPK";
                    begin
                        ItemPriceDatasetByLocMgt.DrillDownCostValueEntries(Rec);
                    end;
                }
                field("Manual Unit Cost (ACY)"; Rec."Manual Unit Cost (ACY)")
                {
                }
                field("Average Unit Price (ACY)"; Rec."Average Unit Price (ACY)")
                {
                    ToolTip = 'Specifies the calculated average unit price. Click to view the underlying Value Entry records used for this calculation.';

                    trigger OnDrillDown()
                    var
                        ItemPriceDatasetByLocMgt: Codeunit "Item Price Dataset By Loc IPK";
                    begin
                        ItemPriceDatasetByLocMgt.DrillDownSalesValueEntries(Rec);
                    end;
                }
                field("Manual Unit Price (ACY)"; Rec."Manual Unit Price (ACY)")
                {
                }
                field("Use Manual Price"; Rec."Use Manual Price")
                {
                }
                field("Average Unit Cost (LCY)"; Rec."Average Unit Cost (LCY)")
                {
                    ToolTip = 'Specifies the calculated average unit cost in local currency.';
                }
                field("Average Unit Price (LCY)"; Rec."Average Unit Price (LCY)")
                {
                    ToolTip = 'Specifies the calculated average unit price in local currency.';
                }
                field("Start Date"; Rec."Start Date")
                {
                }
                field("End Date"; Rec."End Date")
                {
                }
                field("Quantity End of Month IPK"; Rec."Quantity End of Month IPK")
                {
                    ToolTip = 'Specifies the total quantity of the item at the end of the month at this location. Click to view the underlying Item Ledger Entry records.';

                    trigger OnDrillDown()
                    var
                        ItemLedgerEntry: Record "Item Ledger Entry";
                        ItemLedgerEntriesPage: Page "Item Ledger Entries";
                    begin
                        if Rec."Item No." = '' then
                            Error(NoItemSelectedErrLbl);

                        ItemLedgerEntry.Reset();
                        ItemLedgerEntry.SetRange("Item No.", Rec."Item No.");
                        if Rec."Variant Code" <> '' then
                            ItemLedgerEntry.SetRange("Variant Code", Rec."Variant Code");
                        if Rec."Location Code" <> '' then
                            ItemLedgerEntry.SetRange("Location Code", Rec."Location Code");
                        if Rec."End Date" <> 0D then
                            ItemLedgerEntry.SetRange("Posting Date", 0D, Rec."End Date");

                        ItemLedgerEntriesPage.SetTableView(ItemLedgerEntry);
                        ItemLedgerEntriesPage.Run();
                    end;
                }
                field("Base Unit of Measure Code"; Rec."Base Unit of Measure Code")
                {
                }
                field("Currency Code"; Rec."Currency Code")
                {
                }
            }
        }
    }

    actions
    {
        area(Processing)
        {
            group(Functions)
            {
                Caption = 'Functions';
                action(ProcessAllItems)
                {
                    ApplicationArea = All;
                    Caption = 'Process All Items';
                    Image = ItemLedger;
                    Promoted = true;
                    PromotedCategory = Process;
                    PromotedIsBig = true;
                    PromotedOnly = true;
                    ToolTip = 'Process all items to create location-specific dataset records.';

                    trigger OnAction()
                    var
                        ItemPriceDatasetByLocMgt: Codeunit "Item Price Dataset By Loc IPK";
                    begin
                        if Confirm('This will process all items to create location-specific price dataset records. Continue?') then
                            ItemPriceDatasetByLocMgt.ProcessAllItems();
                    end;
                }
                action(ProcessSelectedLocation)
                {
                    ApplicationArea = All;
                    Caption = 'Process Selected Location';
                    Image = Process;
                    Promoted = true;
                    PromotedCategory = Process;
                    PromotedIsBig = true;
                    PromotedOnly = true;
                    ToolTip = 'Process items for a specific location only.';

                    trigger OnAction()
                    var
                        ItemPriceDatasetByLocMgt: Codeunit "Item Price Dataset By Loc IPK";
                        LocationCode: Code[10];
                    begin
                        if Rec."Location Code" <> '' then
                            LocationCode := Rec."Location Code"
                        else begin
                            Message('Please select a record with a location code first.');
                            exit;
                        end;

                        //if Confirm('This will process all items for location %1. Continue?', LocationCode) then
                        ItemPriceDatasetByLocMgt.ProcessItemsForLocation(LocationCode);
                    end;
                }
                action(ClearData)
                {
                    ApplicationArea = All;
                    Caption = 'Clear All Records';
                    Image = ClearLog;
                    Promoted = true;
                    PromotedCategory = Process;
                    PromotedIsBig = true;
                    PromotedOnly = true;
                    ToolTip = 'Clear all records from the location-specific dataset.';

                    trigger OnAction()
                    var
                        ItemPriceDatasetByLoc: Record "Item Price Dataset By Loc IPK";
                    begin
                        if Confirm('This will delete all records from the location-specific price dataset. Are you sure?') then begin
                            ItemPriceDatasetByLoc.DeleteAll(true);
                            Message('All records have been deleted.');
                            CurrPage.Update();
                        end;
                    end;
                }
            }
            group(Reports)
            {
                Caption = 'Reports';
                action(ShowSalesValueEntries)
                {
                    ApplicationArea = All;
                    Caption = 'Value Entries (Sales)';
                    Image = SalesInvoice;
                    Promoted = true;
                    PromotedCategory = Report;
                    PromotedIsBig = true;
                    PromotedOnly = true;
                    ToolTip = 'View the value entries (sales invoices and credit memos) that were used to calculate the average price for this dataset record.';

                    trigger OnAction()
                    var
                        ValueEntry: Record "Value Entry";
                        ValueEntriesPage: Page "Value Entries";
                    begin
                        if Rec."Item No." = '' then
                            Error(NoItemSelectedErrLbl);

                        ValueEntry.SetRange("Item No.", Rec."Item No.");
                        if Rec."Variant Code" <> '' then
                            ValueEntry.SetRange("Variant Code", Rec."Variant Code");
                        if Rec."Location Code" <> '' then
                            ValueEntry.SetRange("Location Code", Rec."Location Code");
                        if (Rec."Start Date" <> 0D) and (Rec."End Date" <> 0D) then
                            ValueEntry.SetRange("Posting Date", Rec."Start Date", Rec."End Date");
                        // Filter for Sale entry type
                        ValueEntry.SetRange("Item Ledger Entry Type", ValueEntry."Item Ledger Entry Type"::Sale);
                        // Filter for Sales Invoice and Sales Credit Memo document types
                        ValueEntry.SetFilter("Document Type", '%1|%2',
                            ValueEntry."Document Type"::"Sales Invoice",
                            ValueEntry."Document Type"::"Sales Credit Memo");
                        ValueEntry.SetFilter("Entry Type", '<>%1', ValueEntry."Entry Type"::Rounding);

                        ValueEntriesPage.SetTableView(ValueEntry);
                        ValueEntriesPage.Run();
                    end;
                }
                action(ShowPurchaseValueEntries)
                {
                    ApplicationArea = All;
                    Caption = 'Value Entries (Purchase)';
                    Image = Purchase;
                    Promoted = true;
                    PromotedCategory = Report;
                    PromotedIsBig = true;
                    PromotedOnly = true;
                    ToolTip = 'View the value entries (purchase invoices, credit memos, and production output) that were used to calculate the average cost for this dataset record.';

                    trigger OnAction()
                    var
                        ValueEntry: Record "Value Entry";
                        ValueEntriesPage: Page "Value Entries";
                    begin
                        if Rec."Item No." = '' then
                            Error(NoItemSelectedErrLbl);

                        ValueEntry.SetRange("Item No.", Rec."Item No.");
                        if Rec."Variant Code" <> '' then
                            ValueEntry.SetRange("Variant Code", Rec."Variant Code");
                        if Rec."Location Code" <> '' then
                            ValueEntry.SetRange("Location Code", Rec."Location Code");
                        if (Rec."Start Date" <> 0D) and (Rec."End Date" <> 0D) then
                            ValueEntry.SetRange("Posting Date", Rec."Start Date", Rec."End Date");
                        // Filter for Purchase and Output entry types
                        ValueEntry.SetFilter("Item Ledger Entry Type", '%1|%2',
                            ValueEntry."Item Ledger Entry Type"::Purchase,
                            ValueEntry."Item Ledger Entry Type"::Output);
                        // Filter for Purchase Invoice, Purchase Credit Memo, and blank document types
                        ValueEntry.SetFilter("Document Type", '%1|%2|%3',
                            ValueEntry."Document Type"::"Purchase Invoice",
                            ValueEntry."Document Type"::"Purchase Credit Memo",
                            ValueEntry."Document Type"::" ");
                        ValueEntry.SetFilter("Entry Type", '<>%1', ValueEntry."Entry Type"::Rounding);
                        ValueEntry.SetRange(Adjustment, false);

                        ValueEntriesPage.SetTableView(ValueEntry);
                        ValueEntriesPage.Run();
                    end;
                }
            }
        }
    }

    var
        NoItemSelectedErrLbl: Label 'No item selected.';
}
