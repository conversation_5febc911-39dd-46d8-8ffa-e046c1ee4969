pageextension 60034 "Item Ledger Entries IPK" extends "Item Ledger Entries"
{
    layout
    {
        modify("Lot No.")
        {
            Visible = true;
        }
        addafter("Lot No.")
        {

            field("Source Lot No. IPK"; Rec."Source Lot No. IPK")
            {
                ApplicationArea = All;
                ToolTip = 'Specifies the value of the Source Lot No. IPK field.';
            }
            // field("Item Category Desc. IPK"; Rec."Item Category Desc.")
            // {
            //     ApplicationArea = All;
            //     ToolTip = 'Specifies the value of the Item Category Description field.';
            // }
        }
        modify("Variant Code")
        {
            Visible = true;
        }
        addafter("Entry No.")
        {

        }
    }
    actions
    {
        addfirst("F&unctions")
        {
            action("ViewPalettes IPK")
            {
                ApplicationArea = All;
                Caption = 'View Palettes', Comment = 'TRK="Paletleri Görüntüle"';
                Promoted = true;
                PromotedCategory = Process;
                PromotedIsBig = true;
                Image = ViewDescription;
                ToolTip = 'Executes the View Palettes action.';

                trigger OnAction()
                var
                    PackageNoInformation: Record "Package No. Information";
                begin
                    PackageNoInformation.SetRange("Location Code IPK", Rec."Location Code");
                    PackageNoInformation.SetRange("Line Item No. IPK", Rec."Item No.");
                    PackageNoInformation.SetRange("Line Variant Code IPK", Rec."Variant Code");
                    PackageNoInformation.SetRange(Blocked, false);
                    PackageNoInformation.SetFilter("Total Quantity IPK", '>0');

                    Page.Run(Page::"Package No. Information List", PackageNoInformation);
                end;
            }
            action("Source Lot No Migration IPK")
            {
                ApplicationArea = All;
                Caption = 'Source Lot No. Migration';
                Promoted = true;
                PromotedCategory = Process;
                PromotedIsBig = true;
                Image = UpdateDescription;
                ToolTip = 'Opens the Source Lot No. migration tool to populate historical data.';

                trigger OnAction()
                var
                    SourceLotNoMigrationPage: Page "Source Lot No. Migration IPK";
                begin
                    SourceLotNoMigrationPage.Run();
                end;
            }
            action("Fill Source Lot No for Selected IPK")
            {
                ApplicationArea = All;
                Caption = 'Fill Source Lot No. for Selected';
                Promoted = true;
                PromotedCategory = Process;
                Image = UpdateDescription;
                ToolTip = 'Fills the Source Lot No. IPK field for selected entries only.';

                trigger OnAction()
                var
                    ItemLedgerEntry: Record "Item Ledger Entry";
                    ConfirmManagement: Codeunit "Confirm Management";
                    SelectionMsg: Label 'Processing selected entries...';
                    CompletionMsg: Label 'Source Lot No. migration completed for selected entries.';
                    ConfirmMsg: Label 'Fill Source Lot No. for %1 selected entries?', Comment = '%1=SelectedCount';
                    NoEntriesMsg: Label 'No entries selected.';
                begin
                    CurrPage.SetSelectionFilter(ItemLedgerEntry);

                    if ItemLedgerEntry.IsEmpty() then begin
                        Message(NoEntriesMsg);
                        exit;
                    end;

                    if not ConfirmManagement.GetResponse(StrSubstNo(ConfirmMsg, ItemLedgerEntry.Count()), false) then
                        exit;

                    Message(SelectionMsg);
                    MigrateSelectedEntries(ItemLedgerEntry);
                    Message(CompletionMsg);
                end;
            }
        }
    }

    local procedure MigrateSelectedEntries(var ItemLedgerEntry: Record "Item Ledger Entry")
    var
        UpdatedCount: Integer;
    begin
        if ItemLedgerEntry.FindSet() then
            repeat
                case ItemLedgerEntry."Entry Type" of
                    ItemLedgerEntry."Entry Type"::Output:
                        if (ItemLedgerEntry."Source Lot No. IPK" = '') and (ItemLedgerEntry."Lot No." <> '') then begin
                            ItemLedgerEntry."Source Lot No. IPK" := ItemLedgerEntry."Lot No.";
                            ItemLedgerEntry.Modify(true);
                            UpdatedCount += 1;
                        end;
                    ItemLedgerEntry."Entry Type"::Consumption:
                        if ItemLedgerEntry."Source Lot No. IPK" = '' then
                            if MigrateConsumptionEntry(ItemLedgerEntry) then
                                UpdatedCount += 1;
                end;
            until ItemLedgerEntry.Next() = 0;

        Message('Updated %1 entries.', UpdatedCount);
    end;

    local procedure MigrateConsumptionEntry(var ConsumptionEntry: Record "Item Ledger Entry"): Boolean
    var
        OutputEntry: Record "Item Ledger Entry";
        TestOutputEntryNo: Integer;
        MaxSearchRange: Integer;
    begin
        // Strategy 1: Look for output entry AFTER the consumption entry (consumption feeds into output)
        MaxSearchRange := 10; // Search up to 10 entries ahead

        OutputEntry.Reset();
        OutputEntry.SetRange("Entry Type", OutputEntry."Entry Type"::Output);
        OutputEntry.SetRange("Document No.", ConsumptionEntry."Document No.");
        OutputEntry.SetFilter("Lot No.", '<>%1', '');

        // Try specific entry numbers first
        for TestOutputEntryNo := ConsumptionEntry."Entry No." + 1 to ConsumptionEntry."Entry No." + MaxSearchRange do begin
            OutputEntry.SetRange("Entry No.", TestOutputEntryNo);
            if OutputEntry.FindFirst() then begin
                ConsumptionEntry."Source Lot No. IPK" := OutputEntry."Lot No.";
                ConsumptionEntry.Modify(true);
                exit(true);
            end;
        end;

        // Strategy 2: Look for any output with higher entry number in same document
        OutputEntry.SetRange("Entry No.");
        OutputEntry.SetFilter("Entry No.", '>%1', ConsumptionEntry."Entry No.");
        OutputEntry.SetCurrentKey("Entry No.");
        if OutputEntry.FindFirst() then begin
            ConsumptionEntry."Source Lot No. IPK" := OutputEntry."Lot No.";
            ConsumptionEntry.Modify(true);
            exit(true);
        end;

        // Strategy 3: Fallback to any output in same document
        OutputEntry.SetRange("Entry No.");
        if OutputEntry.FindFirst() then begin
            ConsumptionEntry."Source Lot No. IPK" := OutputEntry."Lot No.";
            ConsumptionEntry.Modify(true);
            exit(true);
        end;

        exit(false);
    end;
}