table 60037 "Item Price Dataset By Loc IPK"
{
    Caption = 'Item Price Dataset By Location';
    DataClassification = CustomerContent;
    DrillDownPageId = "Item Price Dataset By Loc IPK";
    LookupPageId = "Item Price Dataset By Loc IPK";

    fields
    {
        field(1; "Entry No."; Integer)
        {
            Caption = 'Entry No.';
            AutoIncrement = true;
            ToolTip = 'Specifies the unique entry number for this record.';
        }
        field(2; "Item No."; Code[20])
        {
            Caption = 'Item No.';
            TableRelation = Item."No.";
            ToolTip = 'Specifies the item number.';

            trigger OnValidate()
            var
                Item: Record Item;
            begin
                if Item.Get(Rec."Item No.") then begin
                    Rec.Description := Item.Description;
                    Rec."Base Unit of Measure Code" := Item."Base Unit of Measure";
                end else begin
                    Rec.Description := '';
                    Rec."Base Unit of Measure Code" := '';
                end;
            end;
        }
        field(3; "Variant Code"; Code[10])
        {
            Caption = 'Variant Code';
            ToolTip = 'Specifies the variant code for the item.';

            trigger OnValidate()
            var
                ItemVariant: Record "Item Variant";
            begin
                if (Rec."Variant Code" <> '') and ItemVariant.Get(Rec."Item No.", Rec."Variant Code") then
                    Rec.Description := ItemVariant.Description
                else
                    if Rec."Item No." <> '' then
                        Rec.Validate("Item No.");
            end;
        }
        field(4; "Location Code"; Code[10])
        {
            Caption = 'Location Code';
            TableRelation = Location.Code;
            ToolTip = 'Specifies the location code for the price dataset.';

            trigger OnValidate()
            var
                Location: Record Location;
            begin
                if Location.Get(Rec."Location Code") then
                    Rec."Location Name" := Location.Name
                else
                    Rec."Location Name" := '';
            end;
        }
        field(5; "Location Name"; Text[100])
        {
            Caption = 'Location Name';
            Editable = false;
            ToolTip = 'Specifies the location name.';
        }
        field(6; Description; Text[100])
        {
            Caption = 'Description';
            ToolTip = 'Specifies the description of the item or item variant.';
        }
        field(7; "Source Price Month"; Text[7])
        {
            Caption = 'Source Price Month';
            ToolTip = 'Specifies the reference month for price calculation in MM.YYYY format (e.g., 01.2025).';
        }
        field(8; "Average Unit Price (ACY)"; Decimal)
        {
            Caption = 'Average Unit Price (ACY)';
            DecimalPlaces = 2 : 5;
            ToolTip = 'Specifies the calculated average unit price for the item in Additional Currency (ACY).';
        }
        field(9; "Average Unit Price (LCY)"; Decimal)
        {
            Caption = 'Average Unit Price (LCY)';
            DecimalPlaces = 2 : 5;
            ToolTip = 'Specifies the calculated average unit price for the item in Local Currency (LCY).';
        }
        field(10; "Manual Unit Price (ACY)"; Decimal)
        {
            Caption = 'Manual Unit Price (ACY)';
            DecimalPlaces = 2 : 5;
            ToolTip = 'Specifies the manually entered unit price for the item.';

            trigger OnValidate()
            begin
                UpdateUseManualPrice();
            end;
        }
        field(11; "Average Unit Cost (ACY)"; Decimal)
        {
            Caption = 'Average Unit Cost (ACY)';
            DecimalPlaces = 2 : 5;
            ToolTip = 'Specifies the calculated average unit cost for the item in Additional Currency (ACY).';
        }
        field(12; "Average Unit Cost (LCY)"; Decimal)
        {
            Caption = 'Average Unit Cost (LCY)';
            DecimalPlaces = 2 : 5;
            ToolTip = 'Specifies the calculated average unit cost for the item in Local Currency (LCY).';
        }
        field(13; "Manual Unit Cost (ACY)"; Decimal)
        {
            Caption = 'Manual Unit Cost (ACY)';
            DecimalPlaces = 2 : 5;
            ToolTip = 'Specifies the manually entered unit cost for the item.';

            trigger OnValidate()
            begin
                UpdateUseManualPrice();
            end;
        }
        field(14; "Quantity End of Month IPK"; Decimal)
        {
            Caption = 'Quantity End of Month';
            Editable = false;
            ToolTip = 'Specifies the total quantity of the item at the end of the month at this location.';
        }
        field(15; "Start Date"; Date)
        {
            Caption = 'Start Date';
            ToolTip = 'Specifies the start date of the period for price calculation.';

            trigger OnValidate()
            begin
                if ("Start Date" <> 0D) and ("End Date" <> 0D) and ("Start Date" > "End Date") then
                    Error(StartDateLaterThanEndDateErr);
            end;
        }
        field(16; "End Date"; Date)
        {
            Caption = 'End Date';
            ToolTip = 'Specifies the end date of the period for price calculation.';

            trigger OnValidate()
            begin
                if ("Start Date" <> 0D) and ("End Date" <> 0D) and ("Start Date" > "End Date") then
                    Error(EndDateEarlierThanStartDateErr);
            end;
        }
        field(17; "Base Unit of Measure Code"; Code[10])
        {
            Caption = 'Base Unit of Measure Code';
            TableRelation = "Unit of Measure".Code;
            ToolTip = 'Specifies the base unit of measure code for the item.';
        }
        field(18; "Currency Code"; Code[10])
        {
            Caption = 'Currency Code';
            TableRelation = Currency.Code;
            ToolTip = 'Specifies the currency code for the Additional Currency (ACY) amounts.';
            AllowInCustomizations = Never;
        }
        field(19; "Use Manual Price"; Boolean)
        {
            Caption = 'Use Manual Price';
            ToolTip = 'Specifies whether a manual price or cost has been entered for this record.';
        }
    }

    keys
    {
        key(PK; "Entry No.")
        {
            Clustered = true;
        }
        key(ItemLocationKey; "Item No.", "Variant Code", "Location Code", "Source Price Month")
        {
        }
        key(LocationKey; "Location Code", "Item No.", "Variant Code", "Source Price Month")
        {
        }
        key(DateKey; "Start Date", "End Date")
        {
        }
    }

    fieldgroups
    {
        fieldgroup(DropDown; "Entry No.", "Item No.", "Variant Code", "Location Code", Description, "Base Unit of Measure Code", "Average Unit Price (ACY)", "Average Unit Cost (ACY)", "Currency Code", "Quantity End of Month IPK")
        {
        }
        fieldgroup(Brick; "Item No.", "Location Code", Description, "Base Unit of Measure Code", "Average Unit Price (ACY)", "Average Unit Cost (ACY)", "Source Price Month", "Currency Code", "Quantity End of Month IPK")
        {
        }
    }

    trigger OnInsert()
    begin
        if "Source Price Month" = '' then
            "Source Price Month" := Format(WorkDate(), 0, '<Month,2>.<Year4>');
    end;

    local procedure UpdateUseManualPrice()
    begin
        "Use Manual Price" := ("Manual Unit Cost (ACY)" <> 0) or ("Manual Unit Price (ACY)" <> 0);
    end;

    var
        EndDateEarlierThanStartDateErr: Label 'End Date cannot be earlier than Start Date.';
        StartDateLaterThanEndDateErr: Label 'Start Date cannot be later than End Date.';
}
