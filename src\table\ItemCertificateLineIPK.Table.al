table 60007 "Item Certificate Line IPK"
{
    Caption = 'Item Certificate Line IPK';
    DataClassification = CustomerContent;
    DrillDownPageId = "Item Certificate Line IPK";
    LookupPageId = "Item Certificate Line IPK";

    fields
    {
        field(1; "Item No."; Code[20])
        {
            Caption = 'No.';
            AllowInCustomizations = Always;
        }
        field(2; "Certificate Entry No."; Integer)
        {
            Caption = 'Certificate Entry No.';
            TableRelation = "Item Certificate IPK";
            ToolTip = 'Specifies the Entry No. of the related Item Certificate.';
        }
        field(3; Certificate; Code[20])
        {
            Caption = 'Certificate';
            Editable = false;
            AllowInCustomizations = Always;
            FieldClass = FlowField;
            CalcFormula = lookup("Item Certificate IPK"."Certificate" where("Entry No." = field("Certificate Entry No.")));
            ToolTip = 'Specifies the Certificate from the related Item Certificate.';
        }
        field(4; Raw; Code[20])
        {
            Caption = 'Raw';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup("Item Certificate IPK".Raw where("Entry No." = field("Certificate Entry No.")));
            ToolTip = 'Specifies the Raw value from the related Item Certificate.';
        }
        field(5; Type; Code[30])
        {
            Caption = 'Type';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup("Item Certificate IPK".Type where("Entry No." = field("Certificate Entry No.")));
            ToolTip = 'Specifies the Type value from the related Item Certificate.';
        }
        field(6; "Product Group"; Code[20])
        {
            Caption = 'Product Group';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup("Item Certificate IPK"."Product Group" where("Entry No." = field("Certificate Entry No.")));
            ToolTip = 'Specifies the Product Group from the related Item Certificate.';
        }
        field(7; "Certificate ID"; Code[100])
        {
            Caption = 'Certificate ID';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup("Item Certificate IPK"."Certificate ID" where("Entry No." = field("Certificate Entry No.")));
            ToolTip = 'Specifies the Certificate ID from the related Item Certificate.';
        }
        field(8; "Comment Line For Document"; Text[500])
        {
            Caption = 'Comment Line For Document';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup("Item Certificate IPK"."Comment Line For Document" where("Entry No." = field("Certificate Entry No.")));
            ToolTip = 'Specifies the Comment Line For Document from the related Item Certificate.';
        }
    }
    keys
    {
        key(Key1; "Item No.", "Certificate Entry No.")
        {
            Clustered = true;
        }
    }
}