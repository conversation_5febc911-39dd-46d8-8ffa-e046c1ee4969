tableextension 60045 "Item Ledger Entry IPK" extends "Item Ledger Entry"
{
    fields
    {
        field(60000; "Source Lot No. IPK"; Code[50])
        {
            Caption = 'Source Lot No.';
            ToolTip = 'Specifies the value of the Source Lot No. field.';
        }
        field(60001; "Indent IPK"; Integer)
        {
            Caption = 'Indent';
            ToolTip = 'Specifies the value of the Indent field.';
        }
        // field(60002; "Item Category Desc."; Text[100])
        // {
        //     Caption = 'Item Category Description';
        //     ToolTip = 'Specifies the value of the Item Category Description field.';
        //     FieldClass = FlowField;
        //     CalcFormula = lookup(Item."Item Category Description IPK" where("No." = field("Item No.")));
        // }
    }
}