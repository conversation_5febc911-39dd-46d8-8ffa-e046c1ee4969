pageextension 60080 "Purchase Lines List IPK" extends "Purchase Lines"
{
    layout
    {
        addlast(Control1)
        {
            field("Currency Code IPK"; Rec."Currency Code")
            {
                ApplicationArea = All;
                ToolTip = 'Specifies the currency code from the related Purchase Header.';
            }

            field("Outstanding Quantity IPK"; Rec."Outstanding Quantity")
            {
                ApplicationArea = All;
                ToolTip = 'Specifies the outstanding quantity for the purchase line.';
            }
        }
    }
}
