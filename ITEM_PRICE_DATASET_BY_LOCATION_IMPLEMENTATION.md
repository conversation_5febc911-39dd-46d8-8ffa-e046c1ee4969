# Item Price Dataset By Location - Implementation Guide

## Overview
This implementation extends the existing Item Price Dataset functionality to include location-specific pricing data. The new system allows for item + variant + month + location combinations, providing more granular pricing and costing information per location.

## New Objects Created

### Table: 60037 "Item Price Dataset By Loc IPK"
**Purpose**: Stores location-specific item price dataset records
**Key Features**:
- Extends original Item Price Dataset structure with Location Code
- Maintains monthly granularity (MM.YYYY format)
- Supports both ACY and LCY pricing
- Includes quantity tracking per location
- Automatic calculation of average costs and prices

**Key Fields**:
- `Item No.` - Item identifier
- `Variant Code` - Item variant (optional)
- `Location Code` - Location identifier (new field)
- `Location Name` - Location description (auto-populated)
- `Source Price Month` - Month identifier (MM.YYYY)
- `Average Unit Price (ACY/LCY)` - Calculated sales prices
- `Average Unit Cost (ACY/LCY)` - Calculated purchase costs
- `Manual Unit Price/Cost (ACY)` - Manual overrides
- `Quantity End of Month IPK` - Stock levels at month end
- `Start Date/End Date` - Period boundaries

**Keys**:
1. Primary Key: Entry No.
2. Item Location Key: Item No. + Variant Code + Location Code + Source Price Month
3. Location Key: Location Code + Item No. + Variant Code + Source Price Month
4. Date Key: Start Date + End Date

### Page: 60073 "Item Price Dataset By Loc IPK"
**Purpose**: User interface for viewing and managing location-specific dataset
**Key Features**:
- List view with all essential fields
- Drill-down functionality to Value Entries
- Processing actions for data generation
- Location-specific filtering capabilities

**Actions**:
- `Process All Items` - Generate records for all items at all locations
- `Process Selected Location` - Generate records for specific location
- `Clear All Records` - Remove all dataset records
- `Show Sales/Purchase Value Entries` - Drill down to source data

### Codeunit: 60032 "Item Price Dataset By Loc IPK"
**Purpose**: Business logic for location-specific dataset processing
**Key Features**:
- Comprehensive data processing from Item Ledger Entries
- Location-aware calculations
- Monthly structure creation
- Previous month data propagation
- Performance-optimized processing

**Main Procedures**:
- `ProcessAllItems()` - Process all item/location combinations
- `ProcessItemsForLocation(LocationCode)` - Process specific location
- `DrillDownCostValueEntries()` - Navigate to cost source data
- `DrillDownSalesValueEntries()` - Navigate to sales source data

## Business Logic Implementation

### Data Collection Strategy
1. **Source Identification**: Collects unique Item + Location combinations from Item Ledger Entries
2. **Variant Handling**: Automatically discovers all variants per item/location
3. **Monthly Structure**: Creates records for December 2024 through current month 2025
4. **Quantity Calculation**: Uses CalcSums on Item Ledger Entries per location

### Pricing Calculation Logic
1. **Cost Calculation**: 
   - Sources from Purchase Invoices, Credit Memos, and Adjustments
   - Weighted average based on valued quantities
   - Location-specific filtering
   - ACY/LCY currency handling

2. **Sales Price Calculation**:
   - Sources from Sales Invoices and Credit Memos
   - Weighted average based on valued quantities
   - Location-specific filtering
   - ACY/LCY currency handling

3. **Previous Month Propagation**:
   - Fills gaps when no current month data exists
   - Maintains price continuity
   - Location-specific historical lookup

### Performance Optimizations
- Dictionary-based combination tracking
- Optimized database queries with proper filtering
- Batch processing approach
- Key-based record lookup

## Integration with Existing System

### Relationship to Original Dataset
- **Complementary**: Does not replace existing Item Price Dataset
- **Enhanced Granularity**: Adds location dimension to pricing
- **Same Calculation Logic**: Uses similar algorithms with location filtering
- **Consistent UI**: Follows established patterns from original implementation

### Permission Integration
- Added to existing "Ipek Pamuk Cust. IPK" permission set
- RIMD rights on table data
- Execute rights on codeunit and page

## Usage Scenarios

### 1. Location-Specific Pricing Analysis
- Compare pricing trends across different locations
- Identify location-specific cost variations
- Support location-based decision making

### 2. Inventory Valuation by Location
- Month-end inventory valuations per location
- Location-specific cost tracking
- Multi-location cost analysis

### 3. Sales Performance by Location
- Location-based sales price analysis
- Regional pricing strategy support
- Location profitability assessment

## Technical Considerations

### Object Naming Compliance
- All names follow IPK suffix convention
- Object names within 30-character limit
- Consistent with existing naming patterns

### Database Design
- Efficient indexing strategy
- Optimized for location-based queries
- Supports both item and location filtering

### Error Handling
- Comprehensive validation triggers
- User-friendly error messages
- Data integrity protection

### Currency Handling
- Full ACY (Additional Currency) support
- LCY (Local Currency) calculations
- Currency code tracking per record

## Maintenance and Support

### Data Processing
- Monthly batch processing recommended
- Location-specific processing available
- Clear and rebuild functionality provided

### Monitoring
- Processing time tracking
- Record count reporting
- Performance metrics display

### Troubleshooting
- Drill-down to source Value Entries
- Item Ledger Entry navigation
- Data validation at field level

## Future Enhancements

### Potential Extensions
1. **Job Queue Integration**: Automated monthly processing
2. **Report Generation**: Location-specific pricing reports
3. **API Integration**: External system connectivity
4. **Advanced Filtering**: More granular data selection options
5. **Comparison Tools**: Location vs location analysis features

### Scalability Considerations
- Database indexing optimization for large datasets
- Batch processing enhancements
- Memory optimization for bulk operations
- Query performance tuning

## Conclusion

This implementation successfully extends the Item Price Dataset functionality with location-specific capabilities while maintaining consistency with the existing system architecture. The solution provides enhanced granularity for pricing analysis while preserving performance and user experience standards.
