tableextension 60013 "Posted Whse. Shipment Header" extends "Posted Whse. Shipment Header"
{
    fields
    {
        field(60030; "Load No. IPK"; Code[20])
        {
            Caption = 'Load No.';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup("Load Header IPK"."No." where("Posted Document No." = field("No.")));
            ToolTip = 'Specifies the value of the Load No. field.';
        }
        field(60005; "Vessel Name IPK"; Code[100])
        {
            Caption = 'Vessel Name';
            ToolTip = 'Specifies the value of the Vessel Name field.';
        }
        field(60006; "Voyage No. IPK"; Code[20])
        {
            Caption = 'Voyage No.';
            ToolTip = 'Specifies the value of the Voyage No. field.';
        }
        field(60007; "PoL Code IPK"; Code[10])
        {
            Caption = 'PoL Code';
            TableRelation = "Entry/Exit Point";
            ToolTip = 'Specifies the value of the PoL Code field.';
            trigger OnValidate()
            begin
                Rec.CalcFields("PoL Description IPK");
            end;
        }
        field(60025; "PoL Description IPK"; Text[100])
        {
            Caption = 'PoL Description';
            ToolTip = 'Specifies the value of the PoL Description field.';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup("Entry/Exit Point".Description where(Code = field("PoL Code IPK")));
        }
        field(60008; "PoA Code IPK"; Code[10])
        {
            Caption = 'PoA Code';
            TableRelation = "Entry/Exit Point";
            ToolTip = 'Specifies the value of the PoA Code field.';
            trigger OnValidate()
            begin
                Rec.CalcFields("PoA Description IPK");
            end;
        }
        field(60026; "PoA Description IPK"; Text[100])
        {
            Caption = 'PoA Description';
            ToolTip = 'Specifies the value of the PoA Description field.';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup("Entry/Exit Point".Description where(Code = field("PoA Code IPK")));
        }
        field(60009; "Line IPK"; Code[50])
        {
            Caption = 'Line';
            ToolTip = 'Specifies the value of the Line field.';
        }
        field(60010; "Forwarder IPK"; Code[50])
        {
            Caption = 'Forwarder';
            ToolTip = 'Specifies the value of the Forwarder field.';
        }
        field(60011; "ETS IPK"; Date)
        {
            Caption = 'ETS';
            ToolTip = 'Specifies the value of the ETS field.';
        }
        field(60012; "ETA IPK"; Date)
        {
            Caption = 'ETA';
            ToolTip = 'Specifies the value of the ETA field.';
        }
        field(60013; "Cutt-off Date IPK"; DateTime)
        {
            Caption = 'Cutt-off Date';
            ToolTip = 'Specifies the value of the Cutt-off Date field.';
        }
        field(60014; "Load Count IPK"; Integer)
        {
            Caption = 'Load Count';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = count("Load Header IPK" where("Warehouse Shipment No." = field("No.")));
            ToolTip = 'Specifies the value of the Load Count field.';
        }
        field(60015; "Salesperson Code IPK"; Code[20])
        {
            Caption = 'Salesperson Code';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup(Customer."Salesperson Code" where("No." = field("Source No. INF")));
            ToolTip = 'Specifies the value of the Salesperson Code field.';
        }
        field(60016; "Priority Shipment IPK"; Boolean)
        {
            Caption = 'Priority Shipment';
            ToolTip = 'Specifies the value of the Priority Shipment field.';
        }
        field(60004; "Total Net Weight IPK"; Decimal)
        {
            Caption = 'Total Net Weight';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = sum("Warehouse Shipment Line"."Line Net Weight IPK" where("No." = field("No.")));
            ToolTip = 'Specifies the value of the Total Net Weight field.';
        }
        field(60017; "Total Gross Weight IPK"; Decimal)
        {
            Caption = 'Total Gross Weight';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = sum("Warehouse Shipment Line"."Line Gross Weight IPK" where("No." = field("No.")));
            ToolTip = 'Specifies the value of the Total Gross Weight field.';
        }
        field(60018; "Total Volume IPK"; Decimal)
        {
            Caption = 'Total Volume';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = sum("Warehouse Shipment Line"."Line Volume IPK" where("No." = field("No.")));
            ToolTip = 'Specifies the value of the Total Volume field.';
        }
        field(60019; "Export No. IPK"; Integer)
        {
            Caption = 'Export No.';
            ToolTip = 'Specifies the value of the Export No. field.';
        }
        field(60024; "Total Unit Quantity IPK"; Integer)
        {
            Caption = 'Total Unit Quantity';
            ToolTip = 'Specifies the value of the Total Unit Quantity field.';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = sum("Warehouse Shipment Line"."Line Unit Quantity IPK" where("No." = field("No.")));
        }
        field(60020; "Customs Code IPK"; Code[10])
        {
            Caption = 'Customs Code';
            TableRelation = "Entry/Exit Point";
            ToolTip = 'Specifies the value of the Customs Code field.';
            trigger OnValidate()
            begin
                Rec.CalcFields("Customs Description IPK");
            end;
        }
        field(60027; "Customs Description IPK"; Text[100])
        {
            Caption = 'Customs Description';
            ToolTip = 'Specifies the value of the Customs Description field.';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup("Entry/Exit Point".Description where(Code = field("Customs Code IPK")));
        }
        field(60021; "Customs Officer IPK"; Text[50])
        {
            Caption = 'Customs Officer';
            TableRelation = "Customs Officer IPK".Code;
            ToolTip = 'Specifies the value of the Customs Officer field.';
        }
        field(60022; "Flag IPK"; Text[50])
        {
            Caption = 'Flag';
            ToolTip = 'Specifies the value of the Flag field.';
        }
        field(60023; "E-Export No. IPK"; Code[20])
        {
            AllowInCustomizations = Always;
            Caption = 'E-Export No.';
            ToolTip = 'Specifies the value of the E-Export No. field.';
        }
        field(60028; "Location County IPK"; Text[30])
        {
            AllowInCustomizations = Always;
            Caption = 'Location County';
            ToolTip = 'Specifies the value of the Location County field.';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup(Location.County where(Code = field("Location Code")));
        }
        field(60029; "Vessel Shipping Agent Code IPK"; Text[30])
        {
            Caption = 'Vessel Shipping Agent Code';
            ToolTip = 'Specifies the value of the Vessel Shipping Agent Code field.';
            Editable = true;
            TableRelation = "Shipping Agent";
        }
        field(60000; "Single Shipment Per Order IPK"; Boolean)
        {
            Caption = 'Single Shipment Per Order';
            ToolTip = 'Specifies the value of the Single Shipment Per Order field.';
            AllowInCustomizations = Always;
            DataClassification = ToBeClassified;
        }
    }
    trigger OnDelete()
    var
        DeleteErrorMsg: Label 'You cannot delete posted documents !';
    begin
        Error(DeleteErrorMsg);
    end;
}