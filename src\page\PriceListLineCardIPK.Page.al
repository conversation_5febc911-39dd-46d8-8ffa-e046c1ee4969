page 60064 "Price List Line Card IPK"
{
    ApplicationArea = All;
    Caption = 'Price List Line Card';
    PageType = Card;
    SourceTable = "Price List Line";
    UsageCategory = Administration;

    layout
    {
        area(Content)
        {
            group(General)
            {
                Caption = 'General';

                field("Price List Code"; Rec."Price List Code")
                {
                    Editable = false;
                    ToolTip = 'Specifies the unique identifier of the price list.';
                }
                field("Line No."; Rec."Line No.")
                {
                    Editable = false;
                    ToolTip = 'Specifies the value of the Line No. field.';
                }
                field("Source Type"; Rec."Source Type")
                {
                    Editable = false;
                    ToolTip = 'Specifies the type of the entity that offers the price or the line discount on the product.';
                }
                field("Source No."; Rec."Source No.")
                {
                    Editable = false;
                    ToolTip = 'Specifies the entity to which the prices are assigned. The options depend on the selection in the Assign-to Type field. If you choose an entity, the price list will be used only for that entity.';
                }
                field("Source Name"; PriceListManagement.GetSourceNameFromNo(Rec."Source No."))
                {
                    Caption = 'Source Name';
                    ToolTip = 'Specifies the value of the Source Name field.';
                }
                field("Asset Type"; Rec."Asset Type")
                {
                    Editable = false;
                    ToolTip = 'Specifies the type of the product.';
                }
                field("Asset No."; Rec."Asset No.")
                {
                    ShowMandatory = true;
                    ToolTip = 'Specifies the identifier of the product. If no product is selected, the price and discount values will apply to all products of the selected product type.';
                }
                field("Variant Code"; Rec."Variant Code")
                {
                    ToolTip = 'Specifies the item variant.';
                }
                field(Description; Rec.Description)
                {
                    Editable = false;
                    ToolTip = 'Specifies the description of the product.';
                }
                field("Currency Code"; Rec."Currency Code")
                {
                    ToolTip = 'Specifies the currency that is used for the prices on the price list. The currency can be the same for all prices on the price list, or you can specify a currency for individual lines.';
                }
                field("Unit Price"; Rec."Unit Price")
                {
                    ToolTip = 'Specifies the price of one unit of the selected product.';
                }
                field("Line Unit Price IPK"; Rec."Piece Unit Price IPK")
                {
                }
                field("Units Per Parcel IPK"; Rec."Units Per Parcel IPK")
                {
                }
                // field("Unit Price 2"; LineUnitPrice)
                // {
                //     ToolTip = 'Specifies the price of one unit of the selected product.';
                //     trigger OnValidate()
                // }
                field("Unit of Measure Code"; Rec."Unit of Measure Code")
                {
                    ToolTip = 'Specifies the unit of measure for the product.';
                }
                field("Starting Date"; Rec."Starting Date")
                {
                    ToolTip = 'Specifies the date from which the price is valid.';
                }
                field("Ending Date"; Rec."Ending Date")
                {
                    ToolTip = 'Specifies the last date that the price is valid.';
                }
                field("Minimum Quantity"; Rec."Minimum Quantity")
                {
                    ToolTip = 'Specifies the minimum quantity of the product.';
                }



                field(Status; Rec.Status)
                {
                    ToolTip = 'Specifies whether the price list line is in Draft status and can be edited, Inactive and cannot be edited or used, or Active and used for price calculations.';
                    Editable = false;
                }
                field("Source Group"; Rec."Source Group")
                {
                    ToolTip = 'Specifies the value of the Source Group field.';
                    Visible = false;//must be customer
                }
                field("Source ID"; Rec."Source ID")
                {
                    ToolTip = 'Specifies the value of the Assign-to ID field.';
                    Visible = false;
                }
                field("Amount Type"; Rec."Amount Type")
                {
                    ToolTip = 'Specifies whether the price list line defines prices, discounts, or both.';
                    // Visible = false;//Init value should be price
                }
                field("Price Type"; Rec."Price Type")
                {
                    ToolTip = 'Specifies the price type: sale or purchase price.';
                    // Visible = false;//Must be sale
                }
            }
        }
    }
    // trigger OnAfterGetRecord()
    // var
    //     c: Code[20];
    // begin
    //     c := Rec."Source No."
    // end;

    trigger OnClosePage()
    var
        Customer: Record Customer;
        Vendor: Record Vendor;
        PriceSource: Record "Price Source";
        PriceUXManagement: Codeunit "Price UX Management";
    begin
        if Rec."Asset No." = '' then
            Rec.Delete(true);

        if Customer.Get(Rec."Source No.") then begin
            Customer.ToPriceSource(PriceSource);
            PriceUXManagement.ShowPriceListLines(PriceSource, Enum::"Price Amount Type"::Price);
        end else
            if Vendor.Get(Rec."Source No.") then begin
                Vendor.ToPriceSource(PriceSource);
                PriceUXManagement.ShowPriceListLines(PriceSource, Enum::"Price Amount Type"::Price);
            end;
    end;

    var
        PriceListManagement: Codeunit "Price List Management IPK";
}