pageextension 60075 "Items by Location Matrix IPK" extends "Items by Location Matrix"
{
    layout
    {
        addafter(Description)
        {

            field("Item Category Code IPK"; Rec."Item Category Description IPK")
            {
                ApplicationArea = All;
            }
            field("Pieces on Pallet IPK"; Rec."Pieces on Pallet IPK")
            {
                ApplicationArea = All;
            }
        }
    }
}