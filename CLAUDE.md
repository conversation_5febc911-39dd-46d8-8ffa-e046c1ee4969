# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a Business Central AL extension for İpek Pamuk textile company (ID: db7a14da-6c93-4594-b6da-e687323b5376), providing specialized manufacturing, inventory, and quality control functionality. Version ********* extends core BC tables and adds custom business logic for production order management, lot tracking, package handling, and quality control workflows.

## Development Environment

### VS Code Launch Configuration
- Environment: Sandbox (HC_310725)
- Startup Object: Page 518 or Page 60071 (Source Lot No. Migration IPK)  
- Schema Update Mode: ForceSync for development
- Launch configuration name: "Deniz"

### Build and Code Analysis
- **Code Analyzers**: CodeCop, UICop, PerTenantExtensionCop, BusinessCentral.LinterCop.dll
- **Ruleset**: `infotek.ruleset.json` (company-specific rules with disabled LC0010, LC0068, LC0029, etc.)
- **Root Namespace**: "Infotek"
- **Auto-formatting**: CRS reorganization on save enabled

### Translation
- **Target Language**: Turkish (TRK)
- **Translation Files**: Located in `/Translations/` folder
- **Generated XLIFF**: `Ipek Pamuk Customization.g.xlf`

## Architecture & Code Standards

### Mandatory Naming Conventions
- **ALL objects MUST end with " IPK" suffix** (company identifier) 
- **Object name max length**: 30 characters including IPK suffix
- **File naming**: `<ObjectName>IPK.<TypeAbbreviation>.al`
- **Variable naming**: PascalCase with type included (e.g., `ProductionOrder`, `TempItemLedgerEntry`)

### Object ID Ranges
- **Main range**: 60000-60999 (defined in app.json)
- **Current usage**: Tables 60000+, Pages 60000+, Codeunits 60000+

### Core Architecture Patterns

#### Management Codeunits (Business Logic)
```al
codeunit 60000 "Ipek Production Management IPK" { SingleInstance = true; }
codeunit 60002 "Ipek Sales Management IPK" { SingleInstance = true; }
codeunit 60003 "Ipek Purchase Management IPK" { SingleInstance = true; }
codeunit 60005 "Ipek Quality Management IPK" { SingleInstance = true; }
codeunit 60006 "Ipek Package Trans Mgt IPK" { SingleInstance = true; }
```

#### Table Extension Pattern
```al
tableextension 60XXX "TableName IPK" extends "Original Table"
{
    fields
    {
        field(60000; "Custom Field IPK"; DataType)
        {
            Caption = 'Custom Field IPK';
            DataClassification = CustomerContent;
        }
    }
}
```

#### Directory Structure
```
src/
├── codeunit/           # Business logic and management
├── table/              # New tables
├── tableextension/     # Extensions to BC tables
├── page/               # New pages
├── pageextension/      # Extensions to BC pages
├── enum/               # Enumerations
├── enumextension/      # Enum extensions
├── report/             # Reports and processing
├── reportLayout/       # Report layouts
├── query/              # Query objects
├── permissionset/      # Security permissions
├── controladdin/       # Control add-ins
└── javascript/         # Client-side scripts
```

## Key Business Domains

### Production & Manufacturing
- **Source Lot Tracking**: Critical feature linking consumption entries to output lot numbers (Codeunit 60070)
- **Package Creation**: Custom workflow for creating packages from production orders
- **Lot Number Management**: Automatic lot number generation and tracking
- **Production Order Extensions**: Custom fields for production state tracking

### Inventory & Quality Control
- **Package Transfers**: Moving packages between locations with detailed tracking
- **Quality Control Integration**: Extends existing QCM module functionality
- **Item Certificates**: Certificate management with expiration tracking
- **Inventory Adjustments**: Custom adjustment workflows with approval processes

### Sales & Purchasing
- **Load Planning**: Shipment planning with load optimization
- **Customs Documentation**: Integration with customs officer records
- **Freight Management**: Item charge handling for freight costs

## Dependencies

The extension depends on these Infotek modules:
- İnfotek Add-On Infrastructure (********)
- Quality Control Management (********)
- E-Shipment by İnfotek (********)
- E-Solutions Foundation by İnfotek (*********)
- Quote Order Currency by İnfotek (********)
- Tax and Discount Management by İnfotek (*********)
- E-Invoice by İnfotek (*********)
- Turkish Localization by İnfotek (*********)

## Important Implementation Notes

### Permission Management
- **Single permission set**: `IpekPamukCustIPK.PermissionSet.al`
- **Always update permissions** when adding new objects
- Grants RIMD rights to all custom tables and objects

### Critical Migration System
- **Source Lot No. Migration**: Page 60071 and Codeunit 60070
- Links historical consumption entries to output lot numbers using proximity algorithms
- Essential for lot traceability in production scenarios

### Code Quality Requirements
- Follow AL best practices from `USER_GUIDELINES.md`
- Use microsoft.docs.mcp server for Microsoft technology questions
- Implement proper error handling with meaningful user messages
- Maintain consistent field validation and data classification
- Use flowfields sparingly for performance

### VS Code Settings Applied
- `alOutline.defaultDataClassification`: "CustomerContent"
- Object suffix enforcement: " IPK"  
- Turkish snippet/translation support
- Auto-reorganization and code cop fixes on save

## Development Workflow

1. **Before coding**: Check existing patterns in similar objects
2. **Object creation**: Always add " IPK" suffix and update permission set
3. **Field additions**: Use CustomerContent data classification
4. **Testing**: Use sandbox environment with ForceSync schema updates
5. **Translation**: Update XLIFF files for Turkish localization when adding labels

When modifying this codebase, always maintain the IPK suffix convention, follow established architectural patterns, and ensure new objects are properly permissioned.