page 60071 "Source Lot No. Migration IPK"
{
    Caption = 'Source Lot No. Migration';
    PageType = Card;
    UsageCategory = Tasks;
    ApplicationArea = All;
    Permissions = tabledata "Item Ledger Entry" = rimd;

    layout
    {
        area(Content)
        {
            group(General)
            {
                Caption = 'Migration Options';
                field(Info; InfoText)
                {
                    Caption = 'Information';
                    Editable = false;
                    MultiLine = true;
                    ShowCaption = false;
                    ToolTip = 'Specifies information about the migration process.';
                }
            }
            group(DateRange)
            {
                Caption = 'Date Range Migration';
                field(FromDate; FromDate)
                {
                    Caption = 'From Date';
                    ToolTip = 'Specifies the start date for migration (optional).';
                }
                field(ToDate; ToDate)
                {
                    Caption = 'To Date';
                    ToolTip = 'Specifies the end date for migration (optional).';
                }
            }
            group(Statistics)
            {
                Caption = 'Current Status';
                field(TotalEntries; TotalEntries)
                {
                    Caption = 'Total Entries Needing Migration';
                    Editable = false;
                    ToolTip = 'Specifies the total number of entries that need migration.';
                }
                field(OutputEntries; OutputEntries)
                {
                    Caption = 'Output Entries Missing';
                    Editable = false;
                    ToolTip = 'Specifies the number of output entries missing Source Lot No.';
                }
                field(ConsumptionEntries; ConsumptionEntries)
                {
                    Caption = 'Consumption Entries Missing';
                    Editable = false;
                    ToolTip = 'Specifies the number of consumption entries missing Source Lot No.';
                }
                field(SaleEntries; SaleEntries)
                {
                    Caption = 'Sale Entries Missing';
                    Editable = false;
                    ToolTip = 'Specifies the number of sale entries missing Source Lot No.';
                }
            }
        }
    }

    actions
    {
        area(Processing)
        {
            action(MigrateAll)
            {
                ApplicationArea = All;
                Caption = 'Migrate All Historical Data';
                Image = UpdateDescription;
                Promoted = true;
                PromotedOnly = true;
                PromotedCategory = Process;
                PromotedIsBig = true;
                ToolTip = 'Specifies migration of all historical Item Ledger Entries to populate Source Lot No. field.';

                trigger OnAction()
                begin
                    SourceLotNoMigration.MigrateHistoricalSourceLotNo();
                    RefreshStatistics();
                end;
            }
            action(MigrateDateRange)
            {
                ApplicationArea = All;
                Caption = 'Migrate Date Range';
                Image = CalendarMachine;
                Promoted = true;
                PromotedOnly = true;
                PromotedCategory = Process;
                PromotedIsBig = true;
                ToolTip = 'Specifies migration of Item Ledger Entries within specified date range.';
                //Enabled = (FromDate <> 0D) and (ToDate <> 0D);

                trigger OnAction()
                var
                    SpecifyBothDatesErrLbl: Label 'Please specify both From Date and To Date.';
                    FromDateLaterErrLbl: Label 'From Date cannot be later than To Date.';
                begin
                    if (FromDate = 0D) or (ToDate = 0D) then
                        Error(SpecifyBothDatesErrLbl);

                    if FromDate > ToDate then
                        Error(FromDateLaterErrLbl);

                    SourceLotNoMigration.MigrateSpecificDateRange(FromDate, ToDate);
                    RefreshStatistics();
                end;
            }
            action(ValidateMigration)
            {
                ApplicationArea = All;
                Caption = 'Validate Migration';
                Image = ValidateEmailLoggingSetup;
                Promoted = true;
                PromotedOnly = true;
                PromotedCategory = Process;
                ToolTip = 'Specifies validation of the current state of Source Lot No. migration.';

                trigger OnAction()
                begin
                    SourceLotNoMigration.ValidateMigration();
                    RefreshStatistics();
                end;
            }
            action(MigrateSaleEntries)
            {
                ApplicationArea = All;
                Caption = 'Migrate Sale Entries';
                Image = Sales;
                Promoted = true;
                PromotedOnly = true;
                PromotedCategory = Process;
                PromotedIsBig = true;
                ToolTip = 'Specifies migration of Sale entry types by copying Lot No. to Source Lot No. IPK field.';

                trigger OnAction()
                begin
                    MigrateSaleEntriesProcess();
                    RefreshStatistics();
                end;
            }
            action(RefreshStats)
            {
                ApplicationArea = All;
                Caption = 'Refresh Statistics';
                Image = Refresh;
                Promoted = true;
                PromotedOnly = true;
                PromotedCategory = Process;
                ToolTip = 'Specifies refresh of the migration statistics.';

                trigger OnAction()
                begin
                    RefreshStatistics();
                end;
            }
        }
    }

    trigger OnOpenPage()
    begin
        InitializeInfoText();
        RefreshStatistics();
    end;

    local procedure MigrateSaleEntriesProcess()
    var
        ItemLedgerEntry: Record "Item Ledger Entry";
        ConfirmManagement: Codeunit "Confirm Management";
        ProgressWindow: Dialog;
        UpdatedCount: Integer;
        Counter: Integer;
        TotalRecords: Integer;
        MigrateSaleConfirmMsg: Label 'This will update %1 Sale entries by copying Lot No. to Source Lot No. IPK. Continue?', Comment = '%1 = Number of entries';
        NoSaleEntriesMsg: Label 'No Sale entries found that need Source Lot No. migration.';
        ProcessingMsg: Label 'Processing Sale entries...\Entry #1########## of #2##########', Comment = '%1 = Current entry number, %2 = Total entries';
        SuccessMsg: Label 'Successfully updated %1 Sale entries.', Comment = '%1 = Number of updated entries';
    begin
        // Count sale entries needing migration
        ItemLedgerEntry.Reset();
        ItemLedgerEntry.SetRange("Entry Type", ItemLedgerEntry."Entry Type"::Sale);
        ItemLedgerEntry.SetFilter("Source Lot No. IPK", '=%1', '');
        ItemLedgerEntry.SetFilter("Lot No.", '<>%1', '');
        TotalRecords := ItemLedgerEntry.Count();

        if TotalRecords = 0 then begin
            Message(NoSaleEntriesMsg);
            exit;
        end;

        if not ConfirmManagement.GetResponse(StrSubstNo(MigrateSaleConfirmMsg, TotalRecords), false) then
            exit;

        ProgressWindow.Open(ProcessingMsg);

        if ItemLedgerEntry.FindSet() then
            repeat
                Counter += 1;
                ProgressWindow.Update(1, Counter);
                ProgressWindow.Update(2, TotalRecords);

                if (ItemLedgerEntry."Source Lot No. IPK" = '') and (ItemLedgerEntry."Lot No." <> '') then begin
                    ItemLedgerEntry."Source Lot No. IPK" := ItemLedgerEntry."Lot No.";
                    ItemLedgerEntry.Modify(true);
                    UpdatedCount += 1;
                end;
            until ItemLedgerEntry.Next() = 0;

        ProgressWindow.Close();
        Message(SuccessMsg, UpdatedCount);
    end;

    local procedure InitializeInfoText()
    begin
        InfoText := 'This tool migrates historical Item Ledger Entries to populate the "Source Lot No. IPK" field.' +
                   '\\ • Output entries: Source Lot No. = Lot No.' +
                   '\• Sale entries: Source Lot No. = Lot No.' +
                   '\• Consumption entries: Source Lot No. = Related NEXT output entry''s Lot No.' +
                   '\\The migration uses multiple strategies to link consumption entries to their related output entries:' +
                   '\1. Entry No. proximity (consumption feeds into output - looks for higher Entry No.)' +
                   '\2. Document No. and creation time proximity (finds output created after consumption)' +
                   '\3. Document No. matching as fallback (any output after consumption in same document)' +
                   '\\You can migrate all historical data or specify a date range for partial migration.';
    end;

    local procedure RefreshStatistics()
    var
        ItemLedgerEntry: Record "Item Ledger Entry";
    begin
        // Count total entries needing migration
        ItemLedgerEntry.Reset();
        ItemLedgerEntry.SetFilter("Source Lot No. IPK", '=%1', '');
        TotalEntries := ItemLedgerEntry.Count();

        // Count output entries needing migration
        ItemLedgerEntry.SetRange("Entry Type", ItemLedgerEntry."Entry Type"::Output);
        ItemLedgerEntry.SetFilter("Lot No.", '<>%1', '');
        OutputEntries := ItemLedgerEntry.Count();

        // Count consumption entries needing migration
        ItemLedgerEntry.SetRange("Entry Type", ItemLedgerEntry."Entry Type"::Consumption);
        ItemLedgerEntry.SetRange("Lot No."); // Clear lot no filter for consumption
        ConsumptionEntries := ItemLedgerEntry.Count();

        // Count sale entries needing migration
        ItemLedgerEntry.SetRange("Entry Type", ItemLedgerEntry."Entry Type"::Sale);
        ItemLedgerEntry.SetFilter("Lot No.", '<>%1', '');
        SaleEntries := ItemLedgerEntry.Count();
    end;

    var
        SourceLotNoMigration: Codeunit "Source Lot No. Migration IPK";
        InfoText: Text;
        FromDate: Date;
        ToDate: Date;
        TotalEntries: Integer;
        OutputEntries: Integer;
        ConsumptionEntries: Integer;
        SaleEntries: Integer;
}
