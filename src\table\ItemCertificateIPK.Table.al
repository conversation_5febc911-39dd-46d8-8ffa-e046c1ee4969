table 60008 "Item Certificate IPK"
{
    Caption = 'Item Certificate';
    DataClassification = CustomerContent;
    DrillDownPageId = "Item Certificates IPK";
    LookupPageId = "Item Certificates IPK";


    fields
    {
        field(1; "Entry No."; Integer)
        {
            Caption = 'Entry No.';
            Editable = false;
            AllowInCustomizations = Always;
            ToolTip = 'Specifies the value of the Entry No. field.';
        }
        field(2; Raw; Code[20])
        {
            Caption = 'Raw';
            ToolTip = 'Specifies the value of the Raw field.';
        }
        field(3; Type; Code[30])
        {
            Caption = 'Type';
            AllowInCustomizations = Always;
            ToolTip = 'Specifies the value of the Type field.';
        }
        field(4; Certificate; Code[20])
        {
            Caption = 'Certificate';
            ToolTip = 'Specifies the value of the Certificate field.';
        }
        field(5; "Product Group"; Code[20])
        {
            Caption = 'Product Group';
            ToolTip = 'Specifies the value of the Product Group field.';
        }
        field(6; "Certificate ID"; Code[100])
        {
            Caption = 'Certificate ID';
            ToolTip = 'Specifies the value of the Certificate ID field.';
        }
        field(7; "Comment Line For Document"; Text[500])
        {
            Caption = 'Comment Line For Document';
            ToolTip = 'Specifies the value of the Comment Line For Document field.';
        }

    }
    keys
    {
        key(PK; "Entry No.")
        {
            Clustered = true;
        }
    }
    fieldgroups
    {
        fieldgroup(DropDown; "Entry No.", "Certificate ID", "Comment Line For Document") { }
    }

    trigger OnInsert()
    var
        ItemCertificateIPK: Record "Item Certificate IPK";
    begin
        if "Entry No." = 0 then
            if ItemCertificateIPK.FindLast() then
                "Entry No." := ItemCertificateIPK."Entry No." + 10000
            else
                "Entry No." := 10000;
    end;

    trigger OnDelete()
    var
        ItemCertificateLineIPK: Record "Item Certificate Line IPK";
        ConfirmManagement: Codeunit "Confirm Management";
        ConfirmDeleteMsg: Label 'Do you want to delete the used certificates too ?';
    begin
        if ConfirmManagement.GetResponse(ConfirmDeleteMsg) then begin
            ItemCertificateLineIPK.SetRange("Certificate Entry No.", Rec."Entry No.");
            ItemCertificateLineIPK.DeleteAll(true);
        end;

    end;

}